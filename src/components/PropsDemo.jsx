// 父组件
import { useState } from "react";
import ChildComponent from "./ChildComponent";

function PropsDemo({ styles }) {
  const [message, setMessage] = useState("来自父组件的消息");
  const [count, setCount] = useState(0);

  // 这个函数将传递给子组件
  const handleChildClick = (text) => {
    alert(`子组件说: ${text}`);
    setCount(count + 1);
  };

  return (
    <div className={styles["props-demo"]}>
      <h2>Props 示例</h2>
      <p>父组件状态: {count}</p>
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="修改消息"
        className={styles["learn-input"]}
      />

      {/* 传递props给子组件 */}
      <ChildComponent
        message={message}
        count={count}
        onButtonClick={handleChildClick}
        styles={styles}
      />
    </div>
  );
}

export default PropsDemo;

/*
Vue中的对应实现:

<template>
  <div class="props-demo">
    <h2>Props 示例</h2>
    <p>父组件状态: {{ count }}</p>
    <input
      type="text"
      v-model="message"
      placeholder="修改消息"
    />

    <!-- 传递props给子组件 -->
    <child-component
      :message="message"
      :count="count"
      @button-click="handleChildClick"
    />
  </div>
</template>

<script>
import ChildComponent from './ChildComponent.vue';

export default {
  components: {
    ChildComponent
  },
  data() {
    return {
      message: '来自父组件的消息',
      count: 0
    }
  },
  methods: {
    handleChildClick(text) {
      alert(`子组件说: ${text}`);
      this.count += 1;
    }
  }
}
</script>
*/
