import React, { useState } from "react";

const ClosureTrapDemo = () => {
  const [count, setCount] = useState(0);
  const [log, setLog] = useState([]);

  // 错误方法 - 明显的闭包陷阱
  const handleBadIncrement = () => {
    const currentCount = count; // 明确捕获当前值
    console.log(`点击时捕获的count值: ${currentCount}`);

    setTimeout(() => {
      console.log(`定时器执行，使用捕获的值: ${currentCount}`);
      setCount(currentCount + 1); // 使用捕获的值
      setLog((prev) => [
        ...prev,
        `错误方法: 捕获值${currentCount} -> ${currentCount + 1}`,
      ]);
    }, 500); // 缩短到500ms便于测试
  };

  // 正确方法 - 函数式更新
  const handleGoodIncrement = () => {
    console.log(`点击时的count值: ${count}`);

    setTimeout(() => {
      console.log(`定时器执行，使用函数式更新`);
      setCount((prev) => {
        console.log(`函数式更新中的prev值: ${prev}`);
        const newValue = prev + 1;
        setLog((prevLog) => [
          ...prevLog,
          `正确方法: prev=${prev} -> ${newValue}`,
        ]);
        return newValue;
      });
    }, 500);
  };

  const clearLog = () => {
    setLog([]);
    setCount(0);
  };

  return (
    <div style={{ padding: "20px", border: "2px solid #333", margin: "10px" }}>
      <h2>闭包陷阱演示</h2>

      <div style={{ marginBottom: "20px" }}>
        <h3>当前计数: {count}</h3>
        <button
          onClick={handleBadIncrement}
          style={{
            marginRight: "10px",
            padding: "10px",
            backgroundColor: "#ff6b6b",
          }}
        >
          错误方法 (闭包陷阱)
        </button>
        <button
          onClick={handleGoodIncrement}
          style={{
            marginRight: "10px",
            padding: "10px",
            backgroundColor: "#51cf66",
          }}
        >
          正确方法 (函数式更新)
        </button>
        <button
          onClick={clearLog}
          style={{ padding: "10px", backgroundColor: "#868e96" }}
        >
          清空重置
        </button>
      </div>

      <div>
        <h4>操作日志:</h4>
        <div
          style={{
            height: "200px",
            overflowY: "scroll",
            border: "1px solid #ccc",
            padding: "10px",
            backgroundColor: "#f8f9fa",
          }}
        >
          {log.map((entry, index) => (
            <div key={index} style={{ marginBottom: "5px", fontSize: "14px" }}>
              {index + 1}. {entry}
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: "20px", fontSize: "14px", color: "#666" }}>
        <p>
          <strong>测试说明:</strong>
        </p>
        <p>1. 快速点击"错误方法"按钮多次（在0.5秒内）</p>
        <p>2. 观察每次点击都捕获相同的count值</p>
        <p>3. 快速点击"正确方法"按钮多次</p>
        <p>4. 观察函数式更新中prev值的正确递增</p>
      </div>
    </div>
  );
};

export default ClosureTrapDemo;
