import { useState, useEffect } from "react";

function EffectDemo({ styles }) {
  const [count, setCount] = useState(0);
  const [name, setName] = useState("");

  // 1. 相当于 mounted 和 updated 的组合
  useEffect(() => {
    console.log("组件渲染完成或更新完成");

    // 相当于 beforeUnmount
    return () => {
      console.log("组件即将卸载或更新前的清理");
    };
  });

  // 2. 仅在组件挂载时执行一次，相当于 mounted
  useEffect(() => {
    console.log("组件仅挂载一次，相当于mounted");

    // 模拟API调用
    const timer = setTimeout(() => {
      console.log("数据加载完成");
    }, 1000);

    // 清理函数，相当于 beforeUnmount
    return () => {
      clearTimeout(timer);
      console.log("组件卸载，清理资源");
    };
  }, []); // 空数组表示仅在挂载和卸载时执行

  // 3. 仅在count变化时执行，相当于Vue的watch
  useEffect(() => {
    console.log(`count变化了，新值为: ${count}`);
    document.title = `点击了 ${count} 次`;

    if (count > 10) {
      alert("点击次数超过10次！");
    }
  }, [count]); // 仅在count变化时执行

  return (
    <div className={styles["effect-demo"]}>
      <h2>Effect 示例（生命周期）</h2>
      <p>计数: {count}</p>
      <button
        onClick={() => setCount(count + 1)}
        className={styles["learn-button"]}
      >
        增加
      </button>

      <div style={{ marginTop: "20px" }}>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="输入名字"
          className={styles["learn-input"]}
        />
        <p>你好, {name || "访客"}</p>
      </div>
    </div>
  );
}

export default EffectDemo;

/*
Vue中的对应实现:

<template>
  <div class="effect-demo">
    <h2>生命周期示例</h2>
    <p>计数: {{ count }}</p>
    <button @click="count++">增加</button>

    <div style="margin-top: 20px">
      <input
        type="text"
        v-model="name"
        placeholder="输入名字"
      />
      <p>你好, {{ name || '访客' }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      count: 0,
      name: ''
    }
  },
  mounted() {
    console.log('组件挂载完成');

    // 模拟API调用
    this.timer = setTimeout(() => {
      console.log('数据加载完成');
    }, 1000);
  },
  updated() {
    console.log('组件更新完成');
  },
  beforeUnmount() {
    clearTimeout(this.timer);
    console.log('组件即将卸载，清理资源');
  },
  watch: {
    count(newVal) {
      console.log(`count变化了，新值为: ${newVal}`);
      document.title = `点击了 ${newVal} 次`;

      if (newVal > 10) {
        alert('点击次数超过10次！');
      }
    }
  }
}
</script>
*/
