// React函数组件
import { useState } from "react";
import styles from "@/pages/Learn.module.css";
import classNames from "classnames";
const cx = classNames.bind(styles);

function HelloWorld() {
  // useState相当于Vue的data
  const [count, setCount] = useState(0);

  // 普通函数相当于Vue的methods
  const increment = () => {
    setCount(count + 1);
  };

  // JSX返回相当于Vue的template
  return (
    <div className={cx("hello-world")}>
      <h1>你好，React世界</h1>
      <p>计数器: {count}</p>
      <button onClick={increment} className={cx("learn-button")}>
        增加
      </button>
    </div>
  );
}

export default HelloWorld;

/*
Vue组件对比:

<template>
  <div class="hello-world">
    <h1>你好，Vue世界</h1>
    <p>计数器: {{ count }}</p>
    <button @click="increment">增加</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      count: 0
    }
  },
  methods: {
    increment() {
      this.count += 1;
    }
  }
}
</script>
*/
