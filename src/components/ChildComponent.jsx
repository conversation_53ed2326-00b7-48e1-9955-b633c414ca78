// 子组件
function ChildComponent({ message, count, onButtonClick, styles }) {
  // 直接通过参数解构获取props，不需要this.props

  return (
    <div
      className={styles["child-component"]}
      style={{ border: "1px solid #ccc", padding: "10px", margin: "10px 0" }}
    >
      <h3>子组件</h3>
      <p>从父组件接收的消息: {message}</p>
      <p>从父组件接收的计数: {count}</p>
      <button
        onClick={() => onButtonClick("我是子组件")}
        className={styles["learn-button"]}
      >
        点击调用父组件方法
      </button>
    </div>
  );
}

// 可以设置默认props
ChildComponent.defaultProps = {
  message: "默认消息",
  count: 0,
};

export default ChildComponent;

/*
Vue中的对应实现:

<template>
  <div class="child-component" style="border: 1px solid #ccc; padding: 10px; margin: 10px 0">
    <h3>子组件</h3>
    <p>从父组件接收的消息: {{ message }}</p>
    <p>从父组件接收的计数: {{ count }}</p>
    <button @click="$emit('button-click', '我是子组件')">
      点击调用父组件方法
    </button>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: String,
      default: '默认消息'
    },
    count: {
      type: Number,
      default: 0
    }
  }
}
</script>
*/
