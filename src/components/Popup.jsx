import React from "react";
import classNames from "classnames/bind";
import styles from "@/style/Popup.module.css";
const cx = classNames.bind(styles);

export default class Popup extends React.Component {
  render() {
    return (
      <div className={cx("popupContainer")}>
        <div className={cx("popupContent")}>
          <div className={cx("popupHeader")}>
            <h2 className={cx("popupTitle")}>
              {this.props.popupTitle ? this.props.popupTitle : "标题"}
            </h2>
          </div>
          <div className={cx("popupBody")}>{this.props.children}</div>
          <div className={cx("popupFooter")}>
            <button
              className={cx("popupFooterButton")}
              onClick={this.props.onConfirm}
            >
              确定
            </button>
            <button
              className={cx("popupFooterButton")}
              onClick={this.props.onCancel}
            >
              取消
            </button>
          </div>
        </div>
      </div>
    );
  }
}
