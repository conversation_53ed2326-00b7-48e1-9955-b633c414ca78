import { useState } from "react";
import useLocalStorage from "../hooks/useLocalStorage.jsx";

function HookDemo({ styles }) {
  // 使用自定义Hook存储用户设置
  const [settings, setSettings] = useLocalStorage("user-settings", {
    theme: "light",
    fontSize: "medium",
    notifications: true,
  });

  // 本地状态，不会持久化
  const [tempSettings, setTempSettings] = useState({ ...settings });
  const [isSaved, setIsSaved] = useState(true);

  // 处理设置变化
  const handleSettingChange = (key, value) => {
    setTempSettings({
      ...tempSettings,
      [key]: value,
    });
    setIsSaved(false);
  };

  // 保存设置
  const saveSettings = () => {
    setSettings(tempSettings);
    setIsSaved(true);
    alert("设置已保存！刷新页面后仍将保留。");
  };

  // 重置设置
  const resetSettings = () => {
    const defaultSettings = {
      theme: "light",
      fontSize: "medium",
      notifications: true,
    };
    setTempSettings(defaultSettings);
    setSettings(defaultSettings);
    setIsSaved(true);
    alert("设置已重置为默认值！");
  };

  return (
    <div className={styles["hook-demo"]}>
      <h2>自定义Hook示例</h2>
      <p>这个组件使用了自定义的useLocalStorage Hook来持久化用户设置。</p>
      <p>即使刷新页面，您的设置也会保留。</p>

      <div className={styles["settings-form"]}>
        <div className={styles["form-group"]}>
          <label>主题：</label>
          <select
            value={tempSettings.theme}
            onChange={(e) => handleSettingChange("theme", e.target.value)}
            className={styles["learn-input"]}
          >
            <option value="light">浅色</option>
            <option value="dark">深色</option>
            <option value="auto">自动</option>
          </select>
        </div>

        <div className={styles["form-group"]}>
          <label>字体大小：</label>
          <select
            value={tempSettings.fontSize}
            onChange={(e) => handleSettingChange("fontSize", e.target.value)}
            className={styles["learn-input"]}
          >
            <option value="small">小</option>
            <option value="medium">中</option>
            <option value="large">大</option>
          </select>
        </div>

        <div className={styles["form-group"]}>
          <label>
            <input
              type="checkbox"
              checked={tempSettings.notifications}
              onChange={(e) =>
                handleSettingChange("notifications", e.target.checked)
              }
            />
            启用通知
          </label>
        </div>

        <div className={styles["settings-actions"]}>
          <button onClick={saveSettings} disabled={isSaved} className={styles["learn-button"]}>
            {isSaved ? "已保存" : "保存设置"}
          </button>
          <button onClick={resetSettings} className={styles["reset-button"]}>
            重置为默认
          </button>
        </div>
      </div>

      <div className={styles["current-settings"]}>
        <h3>当前保存的设置：</h3>
        <pre>{JSON.stringify(settings, null, 2)}</pre>
      </div>
    </div>
  );
}

export default HookDemo;
