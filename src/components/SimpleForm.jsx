import { useState } from "react";

function SimpleForm({ styles }) {
  // 在React中，通常为每个表单字段创建一个状态
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [submitted, setSubmitted] = useState(false);

  // 表单提交处理
  const handleSubmit = (e) => {
    e.preventDefault(); // 阻止默认表单提交行为
    console.log({ name, email, message });
    setSubmitted(true);
  };

  return (
    <div className={styles["form-container"]}>
      {submitted ? (
        <div className={styles["success-message"]}>
          <h3>提交成功！</h3>
          <p>姓名: {name}</p>
          <p>邮箱: {email}</p>
          <p>消息: {message}</p>
          <button
            onClick={() => setSubmitted(false)}
            className={styles["learn-button"]}
          >
            再次提交
          </button>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className={styles["form-group"]}>
            <label htmlFor="name">姓名:</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className={styles["learn-input"]}
            />
          </div>

          <div className={styles["form-group"]}>
            <label htmlFor="email">邮箱:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className={styles["learn-input"]}
            />
          </div>

          <div className={styles["form-group"]}>
            <label htmlFor="message">消息:</label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              required
              className={styles["learn-input"]}
            />
          </div>

          <button type="submit" className={styles["learn-button"]}>
            提交
          </button>
        </form>
      )}
    </div>
  );
}

export default SimpleForm;
