import { useState } from "react";

function ListAndConditional({ styles }) {
  const [items, setItems] = useState([
    { id: 1, name: "苹果", category: "水果", inStock: true },
    { id: 2, name: "香蕉", category: "水果", inStock: true },
    { id: 3, name: "西红柿", category: "蔬菜", inStock: false },
    { id: 4, name: "黄瓜", category: "蔬菜", inStock: true },
    { id: 5, name: "牛奶", category: "饮料", inStock: true },
  ]);

  const [showOutOfStock, setShowOutOfStock] = useState(false);
  const [filter, setFilter] = useState("all");

  // 过滤和排序逻辑
  const filteredItems = items
    .filter((item) => {
      // 条件1: 是否显示缺货商品
      if (!showOutOfStock && !item.inStock) {
        return false;
      }

      // 条件2: 分类过滤
      if (filter !== "all" && item.category !== filter) {
        return false;
      }

      return true;
    })
    .sort((a, b) => a.name.localeCompare(b.name));

  // 添加新项目
  const [newItemName, setNewItemName] = useState("");
  const [newItemCategory, setNewItemCategory] = useState("水果");

  const handleAddItem = () => {
    if (!newItemName.trim()) return;

    const newItem = {
      id: Date.now(),
      name: newItemName,
      category: newItemCategory,
      inStock: true,
    };

    setItems([...items, newItem]);
    setNewItemName("");
  };

  // 切换库存状态
  const toggleStock = (id) => {
    setItems(
      items.map((item) =>
        item.id === id ? { ...item, inStock: !item.inStock } : item
      )
    );
  };

  // 删除项目
  const deleteItem = (id) => {
    setItems(items.filter((item) => item.id !== id));
  };

  return (
    <div className={styles["list-demo"]}>
      <h2>列表和条件渲染</h2>

      {/* 过滤控件 */}
      <div className={styles.filters}>
        <label>
          <input
            type="checkbox"
            checked={showOutOfStock}
            onChange={() => setShowOutOfStock(!showOutOfStock)}
          />
          显示缺货商品
        </label>

        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className={styles["learn-input"]}
        >
          <option value="all">所有分类</option>
          <option value="水果">水果</option>
          <option value="蔬菜">蔬菜</option>
          <option value="饮料">饮料</option>
        </select>
      </div>

      {/* 添加新项目 */}
      <div className={styles["add-item"]}>
        <input
          type="text"
          value={newItemName}
          onChange={(e) => setNewItemName(e.target.value)}
          placeholder="商品名称"
          className={styles["learn-input"]}
        />
        <select
          value={newItemCategory}
          onChange={(e) => setNewItemCategory(e.target.value)}
          className={styles["learn-input"]}
        >
          <option value="水果">水果</option>
          <option value="蔬菜">蔬菜</option>
          <option value="饮料">饮料</option>
        </select>
        <button onClick={handleAddItem} className={styles["learn-button"]}>添加商品</button>
      </div>

      {/* 条件渲染 - 空列表提示 */}
      {filteredItems.length === 0 ? (
        <p>没有匹配的商品</p>
      ) : (
        /* 列表渲染 */
        <ul className={styles["item-list"]}>
          {filteredItems.map((item) => (
            <li
              key={item.id}
              className={!item.inStock ? styles["out-of-stock"] : ""}
            >
              <span>
                {item.name} - {item.category}
              </span>

              {/* 条件渲染 - 库存状态 */}
              <span className={styles["stock-status"]}>
                {item.inStock ? "有货" : "缺货"}
              </span>

              <div className={styles["item-actions"]}>
                              <button onClick={() => toggleStock(item.id)} className={styles["learn-button"]}>
                {item.inStock ? "标记缺货" : "标记有货"}
              </button>
              <button onClick={() => deleteItem(item.id)} className={styles["learn-button"]}>删除</button>
              </div>
            </li>
          ))}
        </ul>
      )}

      {/* 条件渲染 - 统计信息 */}
      <div className={styles.stats}>
        <p>总共: {items.length} 项</p>
        <p>过滤后: {filteredItems.length} 项</p>
        <p>有货: {items.filter((item) => item.inStock).length} 项</p>
      </div>
    </div>
  );
}

export default ListAndConditional;
