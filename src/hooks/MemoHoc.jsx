import React from "react";
// 为组件添加鼠标移动事件，并返回组件
export default function MemoHoc(UserComponent) {
  return class extends React.Component {
    state = {
      x: 0,
      y: 0,
    };
    componentDidMount() {
      document.addEventListener("mousemove", (e) => {
        this.setState({ x: e.clientX, y: e.clientY });
      });
    }
    componentWillUnmount() {
      document.removeEventListener("mousemove", this.handleMouseMove);
    }
    render() {
      return <UserComponent {...this.props} {...this.state} />;
    }
  };
}
