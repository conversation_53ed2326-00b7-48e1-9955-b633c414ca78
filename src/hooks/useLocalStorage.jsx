import { useState, useEffect } from "react";

// 自定义Hook，用于在localStorage中存储和获取数据
function useLocalStorage(key, initialValue) {
  // 创建状态，初始值从localStorage获取或使用传入的初始值
  const [storedValue, setStoredValue] = useState(() => {
    try {
      // 尝试从localStorage获取数据
      const item = window.localStorage.getItem(key);
      // 如果存在则解析JSON，否则返回初始值
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // 如果出错（例如JSON解析错误），返回初始值
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // 当key或storedValue变化时，更新localStorage
  useEffect(() => {
    try {
      // 将值转换为JSON并存储
      window.localStorage.setItem(key, JSON.stringify(storedValue));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  // 返回当前值和设置新值的函数
  return [storedValue, setStoredValue];
}

export default useLocalStorage;

/*
Vue中的等效实现（使用Composition API）:

import { ref, watch } from 'vue';

export function useLocalStorage(key, initialValue) {
  // 创建响应式数据，初始值从localStorage获取或使用传入的初始值
  const storedValue = ref(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // 监听值变化，更新localStorage
  watch(storedValue, (newValue) => {
    try {
      window.localStorage.setItem(key, JSON.stringify(newValue));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, { deep: true });

  return storedValue;
}
*/
