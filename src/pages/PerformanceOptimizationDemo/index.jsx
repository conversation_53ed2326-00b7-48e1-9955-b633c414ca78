import React, { useState, useCallback, useMemo } from "react";
import Son from "./Son";

const MemoSon = React.memo(Son);

function PerformanceOptimizationDemo() {
  const obj = useMemo(
    () => ({
      age: 20,
    }),
    []
  );
  const list = useMemo(() => [1, 2, 3, 4, 5], []);
  const handleClick = useCallback(() => {
    console.log("handleClick");
  }, []);
  const [count, setCount] = useState(0);
  return (
    <div>
      <MemoSon handleClick={handleClick} obj={obj} list={list} />
      {count}
      <button onClick={() => setCount(count + 1)}>+</button>
    </div>
  );
}

export default PerformanceOptimizationDemo;
