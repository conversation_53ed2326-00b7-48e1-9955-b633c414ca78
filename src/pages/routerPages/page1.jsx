import { Outlet, Link, useSearchParams } from "react-router-dom";
// Page1 路由演示页面
import React from "react";

/**
 * Page1 组件
 * 用于演示 React Router 的基本页面
 */
export default function Page1() {
  const [searchParams, setSearchParams] = useSearchParams();
  return (
    <div>
      <h2>Page1 页面</h2>
      <p>这是 Page1 的内容。</p>
      <p>{searchParams.get("name")}</p>
      <Link to="son1">Son1</Link>
      <Link to="son2">Son2</Link>
      <Outlet />
    </div>
  );
}
