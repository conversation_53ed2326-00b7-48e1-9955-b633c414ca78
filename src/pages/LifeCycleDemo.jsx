
import React from "react";
/**
 * LifeCycleDemo 组件演示了React类组件的常用生命周期方法
 */
class LifeCycleDemo extends React.Component {
  constructor(props) {
    super(props);
    // 构造函数：类似于Vue的beforeCreate
    this.state = {
      count: 0,
    };
    console.log("constructor：组件构造，初始化state");
  }

  static getDerivedStateFromProps(props, state) {
    // *该生命周期很少用，通常用于props驱动state的特殊场景，比如：根据props更新state
    // 作用：根据最新的props和当前state，返回一个用于更新state的新对象（常用于props变化时同步state）
    // 返回null：不更新state
    // 返回对象：用该对象合并更新state
    console.log("getDerivedStateFromProps", props, state);
    return null;
  }

  componentDidMount() {
    // 组件挂载完成，类似于Vue的mounted
    console.log("componentDidMount：组件挂载完成，可以进行DOM操作、请求数据等");
  }

  shouldComponentUpdate(nextProps, nextState) {
    // 控制组件是否需要更新，类似于Vue的beforeUpdate
    console.log("shouldComponentUpdate：是否需要更新？", nextProps, nextState);
    return true; // 返回false则阻止更新
  }
  getSnapshotBeforeUpdate(prevProps, prevState) {
    // 在组件更新前获取快照，类似于Vue的beforeUpdate
    console.log(
      "getSnapshotBeforeUpdate：组件更新前获取快照",
      prevProps,
      prevState
    );
    return null;
  }
  componentDidUpdate(prevProps, prevState) {
    // 组件更新完成，类似于Vue的updated
    console.log("componentDidUpdate：组件更新完成", prevProps, prevState);
  }

  componentWillUnmount() {
    // 组件卸载前，类似于Vue的beforeUnmount
    console.log("componentWillUnmount：组件即将卸载，可做清理工作");
  }

  handleAdd = () => {
    this.setState({ count: this.state.count + 1 });
  };

  render() {
    // 渲染函数，类似于Vue的render
    console.log("render：渲染组件");
    return (
      <div>
        <h2>React类组件生命周期演示</h2>
        <p>当前计数：{this.state.count}</p>
        <button onClick={this.handleAdd}>+1</button>
      </div>
    );
  }
}

export default LifeCycleDemo;
