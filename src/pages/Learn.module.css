/* Learn组件及其子组件的样式 */

/* 应用容器 */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
  width: 250px;
  background-color: #282c34;
  color: white;
  padding: 20px;
}

.sidebar h2 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 10px;
}

.sidebar button {
  width: 100%;
  text-align: left;
  padding: 10px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.sidebar button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar button.active {
  background-color: #61dafb;
  color: #282c34;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 30px;
  background-color: #f5f5f5;
  overflow-y: auto;
}

.home-content {
  max-width: 800px;
  margin: 0 auto;
}

.home-content h1 {
  color: #282c34;
  border-bottom: 2px solid #61dafb;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

/* 组件样式 */
.hello-world,
.form-container,
.props-demo,
.effect-demo,
.list-demo {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.learn-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.learn-button {
  background-color: #61dafb;
  color: #333;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.learn-button:hover {
  background-color: #4fa8d1;
}

.success-message {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
  padding: 15px;
  margin-top: 20px;
}

/* 列表样式 */
.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
}

.add-item {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.item-list {
  list-style: none;
  padding: 0;
}

.item-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.item-list li:last-child {
  border-bottom: none;
}

.out-of-stock {
  opacity: 0.6;
}

.stock-status {
  font-size: 14px;
  padding: 3px 8px;
  border-radius: 10px;
  background-color: #f5f5f5;
}

.item-actions {
  display: flex;
  gap: 10px;
}

.stats {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: space-around;
}

/* 子组件样式 */
.child-component {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f9f9f9;
}

/* HookDemo组件样式 */
.hook-demo {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.settings-form {
  margin: 20px 0;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.reset-button {
  background-color: #f44336;
  color: white;
}

.reset-button:hover {
  background-color: #d32f2f;
}

.current-settings {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.current-settings pre {
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
  }

  .add-item {
    flex-direction: column;
  }

  .item-list li {
    flex-direction: column;
    align-items: flex-start;
  }

  .item-actions {
    margin-top: 10px;
  }

  .stats {
    flex-direction: column;
  }
}
