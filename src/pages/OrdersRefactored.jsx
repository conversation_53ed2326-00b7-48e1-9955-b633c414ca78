import React from "react";
import Popup from "@/components/Popup.jsx";

const orderStatus = [
  { statusId: -1, statusText: "全部", color: "black" },
  { statusId: 0, statusText: "进行中", color: "blue" },
  { statusId: 1, statusText: "已完成", color: "green" },
  { statusId: 2, statusText: "已逾期", color: "red" },
];
class FilterBar extends React.Component {
  state = {
    date: "",
    name: "",
    status: "",
  };
  handleFilterChange = (key, value) => {
    this.setState({
      [key]: value,
    });
  };
  handleReset = () => {
    this.setState(
      {
        date: "",
        name: "",
        status: "",
      },
      () => this.props.onReset()
    );
  };
  render() {
    return (
      <div
        className="filter-bar"
        style={{ marginBottom: 20, display: "flex", gap: 20 }}
      >
        <div>
          <label>
            日期
            <input
              value={this.state.date}
              onChange={(e) =>
                this.handleFilterChange("date", e.target.value.trim())
              }
              type="text"
            />
          </label>
        </div>
        <div>
          <label>
            订单号
            <input
              value={this.state.name}
              onChange={(e) => {
                this.handleFilterChange("name", e.target.value.trim());
              }}
              type="text"
            />
          </label>
        </div>
        <div>
          <label>
            状态筛选
            <select
              value={this.state.status}
              onChange={(e) => {
                this.handleFilterChange("status", e.target.value);
              }}
            >
              {orderStatus.map((item) => (
                <option key={item.statusId} value={item.statusId}>
                  {item.statusText}
                </option>
              ))}
            </select>
          </label>
        </div>
        <div>
          <button
            onClick={() =>
              this.props.onSearch({
                date: this.state.date,
                name: this.state.name,
                status: this.state.status,
              })
            }
          >
            搜索
          </button>
          <button onClick={this.handleReset}>重置</button>
        </div>
        <div>
          <button onClick={this.props.onAddOrder}>新增订单</button>
        </div>
      </div>
    );
  }
}
class OrderTable extends React.Component {
  // 获取订单状态文本
  _getOrderShowStatus = (status) => {
    const statusObj = orderStatus.find((item) => item.statusId === status);
    return (
      <span style={{ color: statusObj.color }}>{statusObj.statusText}</span>
    );
  };
  render() {
    return (
      <table style={{ width: "100%" }} border="2">
        <thead>
          <tr>
            <th>订单时间</th>
            <th>订单号</th>
            <th>订单状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          {this.props.orders.map((item) => {
            return (
              <tr key={item.id}>
                <td>{item.date}</td>
                <td>{item.name}</td>
                <td>{this._getOrderShowStatus(item.status)}</td>
                <td>
                  <button onClick={() => this.props.onEditOrder(item)}>
                    编辑
                  </button>{" "}
                  <button onClick={() => this.props.onDeleteOrder(item)}>
                    删除
                  </button>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    );
  }
}
class OrderPopup extends React.Component {
  state = {
    date: "",
    name: "",
    status: "",
  };
  getPopupTitle() {
    return this.props.popupType === "add" ? "新增订单" : "编辑订单";
  }
  componentDidUpdate(prevProps) {
    // 只有弹窗打开时，才更新表单。
    // 判断prevProps.popupVisible是为了防止修改state时,组件重复初始化表单
    if (this.props.popupVisible && !prevProps.popupVisible) {
      this.setState({
        date: this.props.initialForm.date,
        name: this.props.initialForm.name,
        status: this.props.initialForm.status,
      });
    }
  }
  handleFormChange = (key, value) => {
    this.setState({
      [key]: value,
    });
  };
  handlePopupConfirm = () => {
    let form = {
      date: this.state.date,
      name: this.state.name,
      status: this.state.status,
    };
    if (this.props.popupType === "edit") {
      form = {
        ...this.props.initialForm,
        ...form,
      };
    }
    this.props.onConfirm(form);
  };
  render() {
    return (
      <>
        {this.props.popupVisible && (
          <Popup
            popupTitle={this.getPopupTitle()}
            onConfirm={this.handlePopupConfirm}
            onCancel={this.props.onCancel}
          >
            <div>
              <label>
                日期
                <input
                  value={this.state.date}
                  onChange={(e) => {
                    this.handleFormChange("date", e.target.value.trim());
                  }}
                  type="text"
                />
              </label>
            </div>
            <div>
              <label>
                订单号
                <input
                  value={this.state.name}
                  onChange={(e) => {
                    this.handleFormChange("name", e.target.value.trim());
                  }}
                  type="text"
                />
              </label>
            </div>
            <div>
              <label>
                状态
                <select
                  value={this.state.status}
                  onChange={(e) => {
                    this.handleFormChange("status", e.target.value);
                  }}
                >
                  {orderStatus.slice(1).map((item) => (
                    <option key={item.statusId} value={item.statusId}>
                      {item.statusText}
                    </option>
                  ))}
                </select>
              </label>
            </div>
          </Popup>
        )}
      </>
    );
  }
}
export default class Orders extends React.Component {
  state = {
    orders: [],
    popupVisible: false,
    popupType: "",
    currentEditOrder: null,
  };
  // 初始化获取订单
  componentDidMount() {
    this.getOrders();
  }
  // 获取订单
  getOrders(params) {
    fetch(`http://localhost:3000/orders?${new URLSearchParams(params)}`)
      .then((res) => res.json())
      .then((data) => {
        this.setState({ orders: data });
      });
  }
  // 搜索
  handleSearch = (params) => {
    this.getOrders(params);
  };
  // 重置
  handleReset = () => {
    this.getOrders();
  };
  // 新增订单
  handleAddOrder = () => {
    this.setState({ popupVisible: true, popupType: "add" });
  };
  // 编辑订单
  handleEditOrder = (item) => {
    this.setState({
      popupVisible: true,
      popupType: "edit",
      currentEditOrder: item,
    });
  };
  // 删除订单
  handleDeleteOrder = async (item) => {
    await fetch(`http://localhost:3000/orders/${item.id}`, {
      method: "DELETE",
    });

    this.getOrders();
  };
  // 新增订单确认
  handleAddOrderConfirm(popupForm) {
    fetch("http://localhost:3000/orders", {
      method: "POST",
      body: JSON.stringify(popupForm),
    })
      .then((res) => res.json())
      .then(() => {
        this.getOrders();
      });
  }

  // 编辑订单确认
  handleEditOrderConfirm(popupForm) {
    const body = {
      date: popupForm.date,
      name: popupForm.name,
      status: popupForm.status,
    };
    fetch(`http://localhost:3000/orders/${popupForm.id}`, {
      method: "PUT",
      body: JSON.stringify(body),
    })
      .then((res) => res.json())
      .then(() => {
        this.getOrders();
      });
  }

  // 确认新增或编辑订单
  handleConfirm = (popupForm) => {
    if (this.state.popupType === "add") {
      this.handleAddOrderConfirm(popupForm);
    } else if (this.state.popupType === "edit") {
      this.handleEditOrderConfirm(popupForm);
    }
    this.setState({
      popupVisible: false,
    });
  };
  // 取消
  handleCancel = () => {
    this.setState({ popupVisible: false });
  };
  render() {
    return (
      <div className="orders-table" style={{ margin: 100 }}>
        <FilterBar
          onSearch={this.handleSearch}
          onReset={this.handleReset}
          onAddOrder={this.handleAddOrder}
        />
        <OrderTable
          orders={this.state.orders}
          onEditOrder={this.handleEditOrder}
          onDeleteOrder={this.handleDeleteOrder}
        />
        <OrderPopup
          popupVisible={this.state.popupVisible}
          initialForm={
            this.state.popupType === "add"
              ? { date: "", name: "", status: "" }
              : {
                  ...this.state.currentEditOrder,
                }
          }
          popupType={this.state.popupType}
          onConfirm={this.handleConfirm}
          onCancel={this.handleCancel}
        />
      </div>
    );
  }
}
