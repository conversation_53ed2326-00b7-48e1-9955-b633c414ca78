import React from "react";
import Popup from "@/components/Popup.jsx";

const orderStatus = [
  { statusId: -1, statusText: "全部", color: "black" },
  { statusId: 0, statusText: "进行中", color: "blue" },
  { statusId: 1, statusText: "已完成", color: "green" },
  { statusId: 2, statusText: "已逾期", color: "red" },
];

export default class Orders extends React.Component {
  state = {
    orders: [],
    filterSearchParams: {
      date: "",
      name: "",
      status: "",
    },
    popupVisible: false,
    popupType: "",
    popupForm: {
      date: "",
      name: "",
      status: "",
    },
  };
  // 初始化获取订单
  componentDidMount() {
    this.getOrders();
  }
  // 获取订单状态文本
  getOrderShowStatus(status) {
    const statusObj = orderStatus.find((item) => item.statusId === status);
    return (
      <span style={{ color: statusObj.color }}>{statusObj.statusText}</span>
    );
  }
  // 获取订单
  getOrders(params) {
    fetch(`http://localhost:3000/orders?${new URLSearchParams(params)}`)
      .then((res) => res.json())
      .then((data) => {
        this.setState({ orders: data });
      });
  }
  // 搜索
  handleSearch() {
    this.getOrders(this.state.filterSearchParams);
  }
  // 重置
  handleReset() {
    this.setState(
      {
        filterSearchParams: {
          date: "",
          name: "",
          status: "",
        },
      },
      () => {
        // 在 setState 的回调中调用，确保状态已更新
        this.getOrders();
      }
    );
  }
  // 新增订单
  handleAddOrder() {
    this.setState({ popupVisible: true, popupType: "add" });
  }
  // 编辑订单
  handleEditOrder(item) {
    this.setState({
      popupVisible: true,
      popupType: "edit",
      popupForm: {
        date: item.date,
        name: item.name,
        status: item.status,
        id: item.id,
      },
    });
  }
  handleDeleteOrder(item) {
    fetch(`http://localhost:3000/orders/${item.id}`, {
      method: "DELETE",
    });
    this.getOrders();
  }
  // 新增订单确认
  handleAddOrderConfirm() {
    fetch("http://localhost:3000/orders", {
      method: "POST",
      body: JSON.stringify(this.state.popupForm),
    })
      .then((res) => res.json())
      .then(() => {
        this.getOrders();
      });
  }

  // 编辑订单确认
  handleEditOrderConfirm() {
    const body = {
      ...this.state.popupForm,
    };
    const id = body.id;
    delete body.id;
    fetch(`http://localhost:3000/orders/${id}`, {
      method: "PUT",
      body: JSON.stringify(body),
    })
      .then((res) => res.json())
      .then(() => {
        this.getOrders();
      });
  }

  // 确认
  handleConfirm() {
    if (this.state.popupType === "add") {
      this.handleAddOrderConfirm();
    } else if (this.state.popupType === "edit") {
      this.handleEditOrderConfirm();
    }
    this.setState({
      popupVisible: false,
      popupForm: { date: "", name: "", status: "" },
    });
  }
  // 取消
  handleCancel() {
    this.setState({ popupVisible: false });
  }
  render() {
    return (
      <div className="orders-table" style={{ margin: 100 }}>
        <div
          className="filter-bar"
          style={{ marginBottom: 20, display: "flex", gap: 20 }}
        >
          <div>
            <label>
              日期
              <input
                value={this.state.filterSearchParams.date}
                onChange={(e) => {
                  this.setState({
                    filterSearchParams: {
                      ...this.state.filterSearchParams,
                      date: e.target.value.trim(),
                    },
                  });
                }}
                type="text"
              />
            </label>
          </div>
          <div>
            <label>
              订单号
              <input
                value={this.state.filterSearchParams.name}
                onChange={(e) => {
                  this.setState({
                    filterSearchParams: {
                      ...this.state.filterSearchParams,
                      name: e.target.value.trim(),
                    },
                  });
                }}
                type="text"
              />
            </label>
          </div>
          <div>
            <label>
              状态筛选
              <select
                value={this.state.filterSearchParams.status}
                onChange={(e) => {
                  this.setState({
                    filterSearchParams: {
                      ...this.state.filterSearchParams,
                      status: e.target.value,
                    },
                  });
                }}
              >
                {orderStatus.map((item) => (
                  <option key={item.statusId} value={item.statusId}>
                    {item.statusText}
                  </option>
                ))}
              </select>
            </label>
          </div>
          <div>
            <button onClick={this.handleSearch.bind(this)}>搜索</button>
            <button onClick={this.handleReset.bind(this)}>重置</button>
          </div>
          <div>
            <button onClick={this.handleAddOrder.bind(this)}>新增订单</button>
          </div>
        </div>
        <table style={{ width: "100%" }}>
          <thead>
            <tr>
              <th>订单时间</th>
              <th>订单号</th>
              <th>订单状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {this.state.orders.map((item) => {
              return (
                <tr key={item.id}>
                  <td>{item.date}</td>
                  <td>{item.name}</td>
                  <td>{this.getOrderShowStatus(item.status)}</td>
                  <td>
                    <button onClick={this.handleEditOrder.bind(this, item)}>
                      编辑
                    </button>
                    <button onClick={this.handleDeleteOrder.bind(this, item)}>
                      删除
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        {this.state.popupVisible && (
          <Popup
            popupTitle="新增订单"
            onConfirm={this.handleConfirm.bind(this)}
            onCancel={this.handleCancel.bind(this)}
          >
            <div>
              <label>
                日期
                <input
                  value={this.state.popupForm.date}
                  onChange={(e) => {
                    this.setState({
                      popupForm: {
                        ...this.state.popupForm,
                        date: e.target.value.trim(),
                      },
                    });
                  }}
                  type="text"
                />
              </label>
            </div>
            <div>
              <label>
                订单号
                <input
                  value={this.state.popupForm.name}
                  onChange={(e) => {
                    this.setState({
                      popupForm: {
                        ...this.state.popupForm,
                        name: e.target.value.trim(),
                      },
                    });
                  }}
                  type="text"
                />
              </label>
            </div>
            <div>
              <label>
                状态
                <select
                  value={this.state.popupForm.status}
                  onChange={(e) => {
                    this.setState({
                      popupForm: {
                        ...this.state.popupForm,
                        status: e.target.value,
                      },
                    });
                  }}
                >
                  {orderStatus.slice(1).map((item) => (
                    <option key={item.statusId} value={item.statusId}>
                      {item.statusText}
                    </option>
                  ))}
                </select>
              </label>
            </div>
          </Popup>
        )}
      </div>
    );
  }
}
