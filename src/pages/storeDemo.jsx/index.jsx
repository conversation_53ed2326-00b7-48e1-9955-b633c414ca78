import { connect } from "react-redux";

function StoreDemo(props) {
  let value = "";
  return (
    <div>
      <p>{props.mes}</p>
      <input onInput={(e) => (value = e.target.value)}></input>
      <button onClick={() => props.changeMes(value)}>修改数据</button>
    </div>
  );
}
export default connect(
  (state) => {
    return { mes: state.mesReducer.mes };
  },
  (dispatch) => {
    return {
      changeMes(payload) {
        dispatch({
          type: "changeMes",
          payload: payload,
        });
      },
    };
  }
)(StoreDemo);

// import store from "@/store";
// function StoreDemo() {
//   console.log("store----", store);
//   const state = store.getState();

//   return (
//     <div>
//       <p>{state.mes}</p>
//       <button
//         onClick={() => {
//           store.dispatch({
//             type: "changeMes",
//             payload: "world",
//           });
//           console.log("state---", state);
//         }}
//       >
//         修改mes
//       </button>
//     </div>
//   );
// }
// export default StoreDemo;
