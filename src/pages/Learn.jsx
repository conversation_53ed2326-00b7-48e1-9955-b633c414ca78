import { useState } from "react";
import HelloWorld from "../components/HelloWorld";
import SimpleForm from "../components/SimpleForm";
import PropsDemo from "../components/PropsDemo";
import EffectDemo from "../components/EffectDemo";
import ListAndConditional from "../components/ListAndConditional";
import HookDemo from "../components/HookDemo";
import styles from "./Learn.module.css";

// 导航菜单数据 - 类似Vue中的data
const NAV_ITEMS = [
  { id: "home", label: "首页" },
  { id: "hello", label: "基础组件" },
  { id: "form", label: "表单处理" },
  { id: "props", label: "Props传递" },
  { id: "effect", label: "生命周期(Effect)" },
  { id: "list", label: "列表与条件渲染" },
  { id: "hook", label: "自定义Hook" },
];

// 首页组件 - 类似Vue中的子组件
function HomeContent() {
  return (
    <div className={styles["home-content"]}>
      <h1>从Vue到React学习指南</h1>
      <p>欢迎来到React学习项目！请从左侧选择一个组件示例来学习。</p>
      <p>这个项目展示了React的核心概念，并与Vue的对应概念进行了对比。</p>
      <p>每个组件文件中都包含了Vue的等效实现作为注释，方便你对比学习。</p>
    </div>
  );
}

// 组件映射表 - 类似Vue中的computed或methods
const COMPONENT_MAP = {
  home: HomeContent,
  hello: HelloWorld,
  form: SimpleForm,
  props: PropsDemo,
  effect: EffectDemo,
  list: ListAndConditional,
  hook: HookDemo,
};

function Learn() {
  // 状态管理 - 类似Vue中的data()
  // activeComponent 相当于 Vue 中的 data: { activeComponent: 'home' }
  const [activeComponent, setActiveComponent] = useState("home");

  // 事件处理函数 - 类似Vue中的methods
  const handleNavClick = (componentId) => {
    setActiveComponent(componentId);
  };

  // 动态渲染组件 - 类似Vue中的computed或v-if
  const renderCurrentComponent = () => {
    const Component = COMPONENT_MAP[activeComponent];
    if (Component) {
      return <Component styles={styles} />;
    }

    return <HomeContent />;
  };

  const showNav = false;
  return (
    <div className={styles["app-container"]}>
      {/* 侧边栏 - 类似Vue中的template */}
      <div className={styles.sidebar}>
        <h2 style={{ color: "white", fontSize: "20px" }}>React学习</h2>
        <nav className={{ "nav-list": showNav }}>
          <ul>
            {/* 列表渲染 - 类似Vue中的v-for */}
            {NAV_ITEMS.map(({ id, label }) => (
              <li key={id}>
                <button
                  className={activeComponent === id ? styles.active : ""}
                  onClick={() => handleNavClick(id)}
                >
                  {label}
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* 主内容区域 */}
      <div className={styles.content}>{renderCurrentComponent()}</div>
    </div>
  );
}

export default Learn;
