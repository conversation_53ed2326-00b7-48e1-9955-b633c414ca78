import React, { useEffect, useState, useCallback, useMemo } from "react";

function User() {
  const [users, setUsers] = useState([]);
  const [search, setSearch] = useState("");
  const [luckyUser, setLuckyUser] = useState(null);
  useEffect(() => {
    console.log("useEffect执行了,依赖为空");
    fetch("https://jsonplaceholder.typicode.com/users")
      .then((res) => res.json())
      .then((data) => setUsers(data));
  }, []); //  依赖项为空数组，表示相当于componentDidMount

  // 使用useMemo缓存过滤后的用户列表，避免每次渲染都重新过滤
  const filteredUsers = useMemo(() => {
    console.log("useMemo执行了,依赖users和search");
    return users.filter((user) => user.name.includes(search));
  }, [users, search]);
  // 依赖项为users和search，表示当users或search发生变化时，重新计算filteredUsers

  const handleSelectLuckyUser = useCallback(() => {
    console.log("useCallback执行了,依赖users");
    const luckyUser = users[Math.floor(Math.random() * users.length)];
    setLuckyUser(luckyUser);
  }, [users]); // 依赖项为users，表示当users发生变化时，重新计算luckyUser

  return (
    <div>
      <h1>用户动态筛选列表</h1>
      <div>
        <input
          type="text"
          onChange={(e) => setSearch(e.target.value)}
          placeholder="搜索用户"
        />
        <button onClick={handleSelectLuckyUser}>选个幸运儿</button>
      </div>
      {luckyUser && <div>幸运儿是：{luckyUser.name}</div>}
      <ul>
        {filteredUsers.map((user) => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    </div>
  );
}

export default User;
