import React from "react";
import { ParentContext, ParentContext2 } from "./ParentContext";
// 父组件

import Son from "./Son";

export default class ContextDemo extends React.Component {
  state = {
    msg: "我是父组件",
  };
  render() {
    return (
      <div>
        {this.state.msg}
        <ParentContext.Provider
          value={{
            name: "小红",
            age: 19,
          }}
        >
          <ParentContext2.Provider
            value={{
              hobby: "篮球",
            }}
          >
            <Son />
          </ParentContext2.Provider>
        </ParentContext.Provider>
      </div>
    );
  }
}
