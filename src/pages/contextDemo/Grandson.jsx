import React from "react";
// 导入祖先组件创建的context
import { ParentContext, ParentContext2 } from "./ParentContext";
// 孙子组件
class Grandson extends React.Component {
  // `static contextType =` 是固定写法，写上这行context才会绑定到this上。
  render() {
    console.log(this.context);
    return (
      <div>
        我是孙子组件
        <div>
          <ParentContext.Consumer>
            {(value) => {
              return (
                <ParentContext2.Consumer>
                  {(value2) => {
                    return (
                      <div>
                        <div>{value.name}</div>
                        <div>{value.age}</div>
                        <div>{value2.hobby}</div>
                      </div>
                    );
                  }}
                </ParentContext2.Consumer>
              );
            }}
          </ParentContext.Consumer>
        </div>
      </div>
    );
  }
}
export default Grandson;
