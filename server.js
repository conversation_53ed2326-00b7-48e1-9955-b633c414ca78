// 使用 ES Module 语法导入模块
import http from 'http';
import url from 'url';

// 1. 定义状态码和文本的映射关系
const STATUS_MAP = {
    0: '进行中',
    1: '已完成',
    2: '已逾期',
};
const UNKNOWN_STATUS_TEXT = '未知状态';

// 辅助函数：为单个订单对象添加 statusText 字段
const transformOrder = (order) => ({
    ...order,
    statusText: STATUS_MAP[order.status] || UNKNOWN_STATUS_TEXT,
});


// 2. 内存中的“数据库”，status 使用数字存储
let orders = [
    { id: 1, date: '2023-05-27', name: '订单0', status: 2 }, // 已逾期
    { id: 2, date: '2023-05-16', name: '订单1', status: 2 }, // 已逾期
    { id: 3, date: '2023-05-03', name: '订单2', status: 1 }, // 已完成
    { id: 4, date: '2023-05-01', name: '订单3', status: 2 }, // 已逾期
    { id: 5, date: '2023-05-07', name: '订单4', status: 1 }, // 已完成
    { id: 6, date: '2023-05-19', name: '订单5', status: 1 }, // 已完成
    { id: 7, date: '2023-05-12', name: '订单6', status: 1 }, // 已完成
    { id: 8, date: '2023-05-19', name: '订单7', status: 2 }, // 已逾期
    { id: 9, date: '2023-05-26', name: '订单8', status: 0 }, // 进行中
    { id: 10, date: '2023-05-20', name: '订单9', status: 1 }, // 已完成
];

let nextId = 11;

// 3. 创建HTTP服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const { pathname, query } = parsedUrl;
    const method = req.method;

    // --- CORS & Headers ---
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    if (method === 'OPTIONS') {
        res.writeHead(204);
        res.end();
        return;
    }
    res.setHeader('Content-Type', 'application/json; charset=utf-8');

    // --- API 路由逻辑 ---

    // A. 获取订单列表 (Read/查询)
    if (method === 'GET' && pathname === '/orders') {
        let results = orders;

        if (query.date) {
            results = results.filter(order => order.date === query.date);
        }
        if (query.name) {
            results = results.filter(order => order.name.includes(query.name));
        }
        // 筛选时，将查询参数转为数字进行比较
        if (query.status && !isNaN(parseInt(query.status, 10))) {
            const statusNum = parseInt(query.status, 10);
            results = results.filter(order => order.status === statusNum);
        }
        
        // 返回前，为每个订单添加 statusText 字段
        const responseData = results.map(transformOrder);

        res.writeHead(200);
        res.end(JSON.stringify(responseData));
    }
    
    // B. 新增订单 (Create/新增)
    else if (method === 'POST' && pathname === '/orders') {
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', () => {
            const newOrderData = JSON.parse(body);
            const newOrder = {
                id: nextId++,
                date: newOrderData.date || new Date().toISOString().split('T')[0],
                name: newOrderData.name,
                // 确保 status 是数字，默认为 0 (进行中)
                status: parseInt(newOrderData.status, 10) || 0,
            };
            orders.push(newOrder);
            res.writeHead(201);
            // 返回新增的订单，并附带 statusText
            res.end(JSON.stringify(transformOrder(newOrder)));
        });
    }

    // C. 修改订单 (Update/修改)
    else if (method === 'PUT' && pathname.startsWith('/orders/')) {
        const id = parseInt(pathname.split('/')[2]);
        let body = '';
        req.on('data', chunk => body += chunk.toString());
        req.on('end', () => {
            const updatedData = JSON.parse(body);
            const orderIndex = orders.findIndex(o => o.id === id);
            if (orderIndex !== -1) {
                // 如果请求中包含status，确保它是数字
                if (updatedData.status !== undefined) {
                    updatedData.status = parseInt(updatedData.status, 10);
                }
                orders[orderIndex] = { ...orders[orderIndex], ...updatedData };
                res.writeHead(200);
                // 返回修改后的订单，并附带 statusText
                res.end(JSON.stringify(transformOrder(orders[orderIndex])));
            } else {
                res.writeHead(404);
                res.end(JSON.stringify({ message: '订单未找到' }));
            }
        });
    }

    // D. 删除订单 (Delete/删除)
    else if (method === 'DELETE' && pathname.startsWith('/orders/')) {
        const id = parseInt(pathname.split('/')[2]);
        const orderIndex = orders.findIndex(o => o.id === id);
        if (orderIndex !== -1) {
            orders.splice(orderIndex, 1);
            res.writeHead(204);
            res.end();
        } else {
            res.writeHead(404);
            res.end(JSON.stringify({ message: '订单未找到' }));
        }
    }
    
    // 其他路由返回 404
    else {
        res.writeHead(404);
        res.end(JSON.stringify({ message: '路由未找到' }));
    }
});

// 4. 启动服务器
const PORT = 3000;
server.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});
