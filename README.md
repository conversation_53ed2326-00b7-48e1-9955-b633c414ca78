# React 学习项目

这是一个用于学习 React 的项目，专为有 Vue 背景的开发者设计。本项目将帮助你在 3 天内掌握 React 的核心概念和开发技能。

## 从 Vue 到 React 的转换指南

### 概念对比

| Vue 概念           | React 对应概念       |
| ------------------ | -------------------- |
| 模板语法           | JSX                  |
| 组件               | 函数组件/类组件      |
| Props              | Props                |
| 数据 (data)        | 状态 (useState)      |
| 计算属性           | useMemo/自定义 hook  |
| 监听器 (watch)     | useEffect            |
| 方法 (methods)     | 普通函数/自定义 hook |
| 生命周期钩子       | useEffect            |
| 指令 (v-if, v-for) | 条件渲染、map 函数   |
| 插槽               | children/props       |
| Vuex               | Context API/Redux    |
| Vue Router         | React Router         |

### React 核心概念

1. **组件** - React 应用由组件构建而成
2. **JSX** - JavaScript 的语法扩展，允许在 JS 中编写类似 HTML 的代码
3. **Props** - 组件间传递数据的方式
4. **状态 (State)** - 组件内部的可变数据
5. **Hooks** - 函数组件中使用状态和其他 React 特性的方法

## 3 天学习计划

### 第 1 天：React 基础

- React 核心概念和工作原理
- JSX 语法
- 组件和 Props
- 状态管理 (useState)
- 事件处理
- 条件渲染和列表

### 第 2 天：React 进阶

- Hooks 深入学习 (useEffect, useContext, useRef)
- 表单处理
- 组件通信模式
- React Router 基础
- 样式处理方案

### 第 3 天：实战应用

- 状态管理方案 (Context API/Redux)
- API 调用和数据获取
- 性能优化
- 常见问题和解决方案
- 构建和部署

## 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 学习资源

- [React 官方文档](https://react.dev/)
- [React Hooks 文档](https://react.dev/reference/react)
- [React Router 文档](https://reactrouter.com/)
- [Redux 文档](https://redux.js.org/)

## 项目结构

```
le-qu/
  - public/          # 静态资源
  - src/
    - assets/        # 图片等资源文件
    - components/    # 可复用组件
    - hooks/         # 自定义 hooks
    - pages/         # 页面组件
    - services/      # API 服务
    - store/         # 状态管理
    - utils/         # 工具函数
    - App.jsx        # 应用入口组件
    - main.jsx       # 应用挂载点
  - index.html       # HTML 模板
  - vite.config.js   # Vite 配置
```

从 Vue 到 React 的核心概念对比
组件创建
Vue: 使用<template>, <script>, <style>三段式结构
React: 使用 JSX，将 HTML 和 JavaScript 混合在一起
状态管理
Vue: data()返回对象或 Composition API 中的 ref/reactive
React: 使用 useState Hook 创建和更新状态
生命周期
Vue: 生命周期钩子如 mounted, updated, beforeUnmount
React: 使用 useEffect Hook 处理不同的生命周期事件
属性传递
Vue: 使用 props 选项定义，使用 v-bind 或简写:传递
React: 通过函数参数接收 props，直接在 JSX 中传递
事件处理
Vue: 使用 v-on 或@绑定事件，使用$emit 发送事件
React: 使用驼峰命名如 onClick 绑定事件，通过 props 传递回调函数
条件渲染
Vue: 使用 v-if, v-else, v-show 指令
React: 使用 JavaScript 的&&, 三元运算符等
列表渲染
Vue: 使用 v-for 指令
React: 使用 map()方法
表单处理
Vue: 使用 v-model 双向绑定
React: 使用 value 和 onChange 手动实现双向绑定
计算属性与监听
Vue: computed 和 watch
React: 使用 useMemo, useCallback 和 useEffect
自定义逻辑复用
Vue: Mixins, Composition API
React: 自定义 Hooks
学习建议
先掌握基础语法：JSX 的语法与 Vue 的模板语法有较大区别，需要适应。
理解组件思想：React 的组件思想与 Vue 类似，但实现方式不同。
熟悉 Hooks API：React 的 Hooks 是最重要的概念，特别是 useState 和 useEffect。
状态管理理念：React 强调单向数据流，与 Vue 的响应式系统有所不同。
项目结构：React 项目结构更加自由，没有 Vue 那样严格的约定。
后续学习路径
React Router：学习前端路由管理
状态管理：学习 Context API 或 Redux
React 性能优化：学习 React.memo, useCallback 等
TypeScript 集成：添加类型系统提高代码质量
测试：学习 React 组件测试
你现在可以通过运行 npm run dev 来启动项目，并通过左侧导航栏浏览不同的示例组件。每个组件都包含了详细的注释，解释了 React 与 Vue 的对应概念。
希望这个项目能帮助你快速从 Vue 过渡到 React！如果有任何问题，随时可以向我提问。
