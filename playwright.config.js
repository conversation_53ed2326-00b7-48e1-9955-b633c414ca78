import { defineConfig, devices } from "@playwright/test";

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: "./tests",
  /* 并行运行测试 */
  fullyParallel: true,
  /* 在CI中失败时禁止重试 */
  forbidOnly: !!process.env.CI,
  /* 在CI中重试失败的测试 */
  retries: process.env.CI ? 2 : 0,
  /* 在CI中选择工作进程数，本地为一半CPU核心数 */
  workers: process.env.CI ? 1 : undefined,
  /* 报告器配置 */
  reporter: [["html"], ["list"]],
  /* 所有测试的共享设置 */
  use: {
    /* 在失败时收集跟踪信息 */
    trace: "on-first-retry",
    /* 截图设置 */
    screenshot: "only-on-failure",
    /* 视频录制 */
    video: "retain-on-failure",
  },

  /* 配置项目用于主要浏览器 */
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },

    {
      name: "firefox",
      use: { ...devices["Desktop Firefox"] },
    },

    {
      name: "webkit",
      use: { ...devices["Desktop Safari"] },
    },

    /* 移动端浏览器测试 */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },
  ],

  /* 在测试开始前启动本地开发服务器 */
  webServer: {
    command: "npm run dev",
    port: 5173,
    reuseExistingServer: !process.env.CI,
  },
});
