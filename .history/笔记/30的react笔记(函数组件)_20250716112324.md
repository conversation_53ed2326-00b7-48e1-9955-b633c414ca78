# React 函数组件学习笔记

## 函数组件和类组件的区别

1. 函数组件没有生命周期
2. 函数组件没有 this
3. 函数组件通过 hook 来完成各种操作
4. 函数组件本身的函数体相当于 render 函数
5. props 在函数的第一个参数接收

### state 的创建和更新

```mermaid
graph TD
  A1["useState"] --> A2["获取到state数据和修改这个数据的专属方法"]
  A2 -- "使用" --> A3["直接把数据拿出来使用"]
  A2 -- "修改" --> A4["调用专属修改方法修改，传入要修改的值"]
```

## Hook 基础

下面只记录了几个常用的 hook，我们也可以自定义 hook。
hook 只能用于函数组件，用在别的地方会报错。

### `useEffect`

定义副作用（用于生命周期模拟、数据监听）:

- 不传第 2 个参数 = `componentDidMount和componentDidUpdate`
- 第二个参数传空数组 = `componentDidMount`
- 第二个参数数组里放某个数据 = `watch` 监听(开始就会执行一次)

### `useMemo`

缓存一个数据，让一段计算在开始运行一次，后续只有以来的数据发生变化时才重新运算。
作用：

1. 类似于 Vue 的计算属性。
2. 缓存一个数据，让它不会重新创建。

### `useCallback`

缓存一个方法，让方法不会每次更新都重新创建。

### `useMemo` 和 `useCallback` 第二个参数

- **不传第 2 个参数：**
  - **效果**：`useMemo` 会在**每一次组件渲染时**都重新执行计算。`useCallback` 会在**每一次组件渲染时**都重新创建一个新的函数。
  - **结论**：这使得它们完全失去了缓存的意义，等同于没有使用这两个 Hook。所以这种情况几乎永远不会使用。
- **第二个参数传空数组 `[]`：**

  - **效果**：`useMemo` 只会在组件**初次渲染后计算一次**，然后永远返回那个缓存的值。`useCallback` 只会在组件**初次渲染后创建一次**，然后永远返回那个缓存的函数实例。
  - **结论**：这完美对应了 `componentDidMount` 的行为模式——“只执行一次”。

- **第二个参数数组里放某个数据 `[dep1, dep2]`：**

  - **效果**：

    - `useMemo` 会在初次渲染时计算一次，然后在后续的渲染中，**只有当 `dep1` 或 `dep2` 发生变化时**，才会重新计算。否则，就一直返回缓存的值。
    - `useCallback` 会在初次渲染时创建一次函数，然后在后续的渲染中，**只有当 `dep1` 或 `dep2` 发生变化时**，才会重新创建函数。否则，就一直返回缓存的函数实例。

  - **结论**：这完美对应了 `watch` 监听的行为模式——“开始就会执行一次，后续只有依赖的数据发生变化时才重新运算/创建”。

`useEffect`, `useMemo`, `useCallback`，第二个参数的规则是一样的。这三个 Hooks 共享了同一套依赖数组 (dependency array) 的规则和逻辑。

### 其他 hook

- `useRef` 函数组件中使用 ref
- `useContext` 更方便的解析 context 的 provider 的数据

### **Hooks 的黄金法则：“用到了什么，就依赖什么。”**

在 `useEffect`, `useMemo`, `useCallback` 的函数体内部，只要用到了任何来自组件作用域的变量（比如 `state` 或 `props`），就**必须**把它们放到依赖项数组中。
忘记添加依赖项是 React Hooks 开发中最常见的 Bug 来源之一。

## HOC 高阶组件

### 什么是 HOC

HOC（Higher-Order Component）高阶组件是一种用于**逻辑复用**的设计模式。它接收一个组件作为参数，返回一个新的增强组件。

**类比理解：**

- 类似于 Vue 的 Mixin 和自定义指令
- 用于在不修改原组件的情况下，为其添加新的功能

### 使用场景

**什么时候使用 HOC：**

- **UI 内容和操作的复用** → 使用普通组件
- **单纯逻辑的复用** → 使用 HOC 高阶组件

**总结：** 当某个操作逻辑或运算经常出现时，就可以提取为高阶组件。

### 实际示例

**使用 HOC 的组件：**

```jsx
// HocDemo.jsx
import React from "react";
import MemoHoc from "../hooks/MemoHoc";

// 原始组件
function Son(props) {
  return (
    <div>
      <p>Son {props.name}</p>
      <p>
        鼠标位置：{props.x} {props.y}
      </p>
    </div>
  );
}

// 使用 HOC 增强组件
const MemoSon = MemoHoc(Son);

// 父组件
function HocDemo() {
  return (
    <div>
      <MemoSon name="张三" />
    </div>
  );
}

export default HocDemo;
```

**HOC 实现：**

```jsx
// MemoHoc.jsx
import React from "react";

// 高阶组件：为组件添加鼠标移动事件监听
export default function MemoHoc(UserComponent) {
  return class extends React.Component {
    state = {
      x: 0,
      y: 0,
    };

    // 绑定鼠标移动事件
    componentDidMount() {
      document.addEventListener("mousemove", (e) => {
        this.setState({ x: e.clientX, y: e.clientY });
      });
    }

    // 移除鼠标移动事件
    componentWillUnmount() {
      document.removeEventListener("mousemove", this.handleMouseMove);
    }
    render() {
      // 将原组件的 props 和新增的 state 都传递给原组件
      return <UserComponent {...this.props} {...this.state} />;
    }
  };
}
```

### HOC 的优势

1. **逻辑复用**：多个组件可以共享同一套逻辑
2. **不侵入原组件**：原组件无需修改，保持纯净
3. **组合性强**：多个 HOC 可以组合使用
4. **易于测试**：逻辑与 UI 分离，便于单元测试

## React 性能问题和优化

### React 性能问题

React 最大的一个性能问题就是-React 的某个组件的更新会带着它的子组件一起更新。
所以，我们需要解决这个问题解决问题。

1. React 在源码层面上尽量弥补这个问题
2. 我们自己的代码，只让子组件做合理的更新

### 源码优化

#### React 的时间切片

**问题背景：**
与 Vue 的依赖收集机制不同，React 没有依赖收集，每次更新都需要进行大量的虚拟 DOM diff 算法对比和计算工作。当更新量过大时，这些计算会占用很长时间，可能阻塞浏览器的主线程，导致页面出现长时间白屏。

**解决方案 - 时间切片：**
React 设计了时间切片（Time Slicing）机制来解决这个问题。其核心思想是：

- 将大的更新任务分割成多个小的时间片
- 每个时间片最多执行 16ms（一帧的时间）
- 执行完一个时间片后，暂停计算，让浏览器进行渲染
- 然后继续执行下一个时间片，如此往复

**优势：**

- 避免长时间阻塞主线程
- 保持页面的响应性
- 提供更好的用户体验

#### Fiber 架构

**为什么需要 Fiber：**
为了支持时间切片，React 需要：

1. 将更新任务分解为一个个可中断的单元
2. 具备恢复上次中断计算进度的能力

**Fiber 的作用：**

- 每个组件都会被转化为一个 Fiber 结构对象
- Fiber 对象包含了组件的状态、props、子组件等信息
- 通过 Fiber 链表结构，React 可以：
  - 追踪当前执行到哪个组件
  - 在中断后从上次的位置继续执行
  - 实现优先级调度和任务恢复

**总结：**
Fiber 是 React 实现时间切片的基础数据结构，让 React 具备了可中断、可恢复的更新能力。

### 个人代码优化

#### 避免父组件更新导致子组件不必要的重新渲染

React 中一个常见的性能问题是：当父组件状态更新时，即使子组件不依赖父组件传递的 props，子组件也会跟着重新渲染。这会造成不必要的性能损耗。

**解决方案：**

- **类组件**：使用 `PureComponent` 或手动实现 `shouldComponentUpdate`
- **函数组件**：使用 `React.memo` 包装组件

函数组件示例：
使用 memo 包装 Son 组件

```jsx
import React, { useState } from "react";
function Son() {
  console.log("Son render");
  return <div>Son</div>;
}

const MemoSon = React.memo(Son);

function PerformanceOptimizationDemo() {
  const [count, setCount] = useState(0);
  return (
    <div>
      <MemoSon />
      {count}
      <button onClick={() => setCount(count)}>+</button>
    </div>
  );
}
```

#### 避免 state 同样的值产生更新

- 避免 state 修改为同样的值，而产生无意义更新（`PureComponent`和函数组件本身就会判断）

函数组件示例：
state 改为相同值，Son render 不会重复触发。

```jsx
import React, { useState } from "react";
function Son() {
  console.log("Son render");
  return <div>Son</div>;
}

function PerformanceOptimizationDemo() {
  const [count, setCount] = useState(0);
  return (
    <div>
      <Son />
      {count}
      <button onClick={() => setCount(count)}>+</button>
    </div>
  );
}
```

### Props 优化与性能提升

在 React 中，即使父组件使用了 `PureComponent` 或 `React.memo`，只要传递给子组件的 props 没有发生变化，子组件就不会重新渲染。但需要特别注意：**如果 props 是函数、对象或数组等引用类型，每次父组件渲染时它们的引用都会变化，导致子组件依然会重新渲染。**

**解决方案**

- **用 `useCallback` 包裹传递给子组件的方法**，确保函数引用稳定。
- **用 `useMemo` 包裹传递的对象或数组**，确保引用不变。

#### 示例代码

```jsx
import React, { useState, useCallback, useMemo } from "react";
import Son from "./Son";

// 用 React.memo 包裹子组件，提升性能
const MemoSon = React.memo(Son);

function PerformanceOptimizationDemo() {
  // 用 useMemo 缓存对象和数组
  const obj = useMemo(() => ({ age: 20 }), []);
  const list = useMemo(() => [1, 2, 3, 4, 5], []);
  // 用 useCallback 缓存函数
  const handleClick = useCallback(() => {
    console.log("handleClick");
  }, []);
  const [count, setCount] = useState(0);

  return (
    <div>
      <MemoSon handleClick={handleClick} obj={obj} list={list} />
      {count}
      <button onClick={() => setCount(count + 1)}>+</button>
    </div>
  );
}
```

#### 注意事项

- `useCallback` 和 `useMemo` **一定要填写第二个依赖参数**，否则不会起到缓存作用。通常传递 `[]` 表示只在组件挂载时创建一次。
- 只有当 props 的引用保持不变时，`React.memo` 才能有效阻止子组件的重复渲染。

#### 类比 Vue

- `useMemo` 类似于 Vue 的计算属性（computed），用于缓存复杂计算结果。
- `useCallback` 类似于 Vue 的 methods，但通过缓存避免函数重复创建。

## react-router

### react-router 的三个版本

- react-router 服务端渲染使用
- react-router-dom 浏览器渲染使用
- react-router-native RN 混合开发使用

### react-router 的使用步骤

1. 通过 BroserRouter 或者 HashRouter 包裹要使用路由的根组件
2. 使用 Routes 组件，定义路由渲染区域

   - 相当于 Vue 的<router-view>

3. 使用 Route 组件，定义具体路由规则

   - 每个 Route 相当于 Vue Router 的一条路由配置。
   - path 属性指定路径，element 属性指定要渲染的组件。

4. 使用 NavLink 或者 Link 组件，定义跳转链接
   - `Link`：普通跳转，类似 Vue 的`router-link`
   - `NavLink`：选中会增加一个`active`类名，适合做导航菜单。

#### 易错点

- `Routes`必须包裹所有`Route`，不能直接写多个`Route`
- `Route` 的`element`必须是 JSX 元素（`<Component/>`），不能是组件名。
- 嵌套路由需要在子组件中再次使用`Routes`和`Route`

实例：

1.定义路由

```jsx
import React from "react";
import { Routes, Route, Navigate, Link } from "react-router-dom";
import Page1 from "./page1";
import Page2 from "./page2";

/**
 * 导航页
 */
function Nav() {
  return (
    <div>
      <Link to="/page1">普通路由page1</Link>
      <Link to="/page2">普通路由page2</Link>
    </div>
  );
}

/**
 * 路由集中管理
 */
export default function RouterPages() {
  return (
    <div>
      <Nav />
      <Routes>
        <Route path="/page1" element={<Page1 />} />
        <Route path="/page2" element={<Page2 />} />
        {/* 兜底重定向到首页 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  );
}
```

### React 全局插件的使用方式

React 中没有像 Vue 那样的`vue.use`方法，react 中使用一个插件、库，都是引入一个组件，然后把要使用该插件的部分包起来。

### 动态路由和嵌套路由

**嵌套路由：**

1. 在 route 内部增加 route

```jsx
<Route path="/page1" element={<Page1 />}>
  <Route path="son1" element={<Son1 />} />
  <Route path="son2" element={<Son2 />} />
</Route>
```

2. 在 page1 页面天机`<outlet/>`用来渲染嵌套的子路由

```jsx
// page1.jsx
import { Outlet, Link } from "react-router-dom";
export default function Page1() {
  return (
    <div>
      <h2>Page1 页面</h2>
      {/*子路由渲染区域*/}
      <Outlet />
    </div>
  );
}
```

**动态路由**
path 和 Vue 的动态路由的写法一致。

```jsx
<Route path="/page3/:id" element={<Page3 />} />
```

### 获取路由参数

| 功能          | v6 Hook 写法        | v5 写法（类组件/props）      | 说明                         |
| ------------- | ------------------- | ---------------------------- | ---------------------------- |
| 路径参数      | `useParams()`       | `this.props.match.params`    | 获取`/user/:id`里面的`id`    |
| 查询参数      | `useSearchParams()` | `this.props.location.search` | 获取 URL 的`?a=1&b=2`部分    |
| Location 信息 | `useLocation()`     | `this.props.location`        | 获取当前路由的 location 对象 |
| 跳转传参      | `useNavigate()`     | `this.props.location.state`  | 跳转是传递的 state 数据      |

#### `useSearchParams()`

`useSearchParams()` 返回的 `searchParams` 是一个基于 `URLSearchParams` 的可迭代对象，常用于获取和操作 URL 查询参数（如 `?a=1&b=2`）。

**常用操作：**

- `searchParams.toString()`返回`a=1&b=2`
- `searchParams.get('a')`  获取参数 `a` 的值（如 `1`）。
- `Object.fromEntries(searchParams)`  将所有参数转为对象（如 `{a: 1, b: 2}`）。
- `[...searchParams]`  转为数组形式（如 `[['a', 1], ['b', 2]]`）。
- `[...searchParams.keys()]`  获取所有参数名（如 `['a', 'b']`）。
- `[...searchParams.values()]`  获取所有参数值（如 `[1, 2]`）。

**遍历方式：**

- `for...of` 循环：

  ```js
  for (const [key, value] of searchParams) {
    console.log(key, value);
    // a 1
    // b 2
  }
  ```

- `forEach` 循环：

  ```js
  searchParams.forEach((value, key) => {
    console.log(key, value);
    // a 1
    // b 2
  });
  ```

  还有很多方法，参考 `URLSearchParams` 文档。

#### `useParams()`

`useParams()` 用于获取路由路径中的参数。例如，路由定义为 `user/:id/:name`，访问 `user/123/ian` 时，`useParams()` 返回 `{id: '123', name: 'ian'}`。

#### `useLocation()`

`useLocation()`返回 Location 对象。

例如，路由定义为 `user/:id/:name`，访问 `user/123/ian` 时，`useLocation()`返回：

```js
{
    "pathname": "/page3/123/ian",
    "search": "",
    "hash": "",
    "state": null,
    "key": "41jkv5aq"
}
```

例如，访问 `user?a=1&b=2` 时，`useLocation()`返回：

```js
{
    "pathname": "/page2",
    "search": "?a=1&b=2",
    "hash": "",
    "state": null,
    "key": "ax5ttjrr"
}
```

#### `useNavigate()`

```jsx
// Page2 路由演示页面
import React from "react";
import { useNavigate } from "react-router-dom";

/**
 * Page2 组件
 * 用于演示 React Router 的基本页面
 */
export default function Page2() {
  const navigate = useNavigate();
  return (
    <div>
      <h2>Page2 页面</h2>
      <p>这是 Page2 的内容。</p>
      <button onClick={() => navigate("/page3/123/哈哈哈")}>跳转page3</button>
    </div>
  );
}
```

### `Navigate` 重定向

使用`Navigate`进行兜底跳转

```jsx
import { Routes, Route, Navigate, Link } from "react-router-dom";

export default function RouterPages() {
  return (
    <Routes>
      <Route path="/page1" element={<Page1 />} />
      {/* 兜底重定向到首页 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}
```

使用`Navigate`进行权限控制，对没有权限的用户进行重定向。

```jsx
import { Routes, Route, Navigate, Link } from "react-router-dom";
import Page1 from "./page1";
import Page2 from "./page2";

const token = localStorage.getItem("token");

export default function RouterPages() {
  return (
    <Routes>
      <Route path="/page1" element={<Page1 />} />
      <Route
        path="/page2"
        element={token ? <Page2 /> : <Navigate to="/page1" replace />}
      ></Route>
    </Routes>
  );
}
```

#### 异步路由

React 提供了两个核心 API 来实现异步路由：

1. `React.lazy()` - 用来动态导入组件
2. `Suspense` - 用于在组件加载时显示加载状态
```

export default function RouterPages() {
  return (
    <div>
      <Nav />
      <Suspense fallback={<div>页面加载中</div>}>
        <Routes>
          <Route path="/page1" element={<Page1 />}>
            <Route path="son1" element={<Son1 />} />
            <Route path="son2" element={<Son2 />} />
          </Route>
          <Route path="/page2" element={<Page2 />}></Route>
          <Route path="/page3/:id/:name" element={<Page3 />} />
          {/* 兜底重定向到首页 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </div>
  );
}
```
####