import { connect } from "react-redux";

function StoreDemo(props) {
  console.log("props---", props);

  return <div></div>;
}
export default connect(
  (state) => {
    return { mes: state.mes };
  },
  (dispatch) => {
    return {};
  }
)(StoreDemo);

// import store from "@/store";
// function StoreDemo() {
//   console.log("store----", store);
//   const state = store.getState();

//   return (
//     <div>
//       <p>{state.mes}</p>
//       <button
//         onClick={() => {
//           store.dispatch({
//             type: "changeMes",
//             payload: "world",
//           });
//           console.log("state---", state);
//         }}
//       >
//         修改mes
//       </button>
//     </div>
//   );
// }
// export default StoreDemo;
