import store from "@/store";
function StoreDemo() {
  console.log("store----", store);
  const state = store.getState();
  console.log("state---", state);

  return (
    <div>
      <p>{state.mes}</p>
      <button
        onClick={() =>
          store.dispatch({
            type: "changeMes",
payload
          })
        }
      >
        修改mes
      </button>
    </div>
  );
}
export default StoreDemo;
