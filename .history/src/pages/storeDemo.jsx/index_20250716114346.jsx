import store from "@/store";
function StoreDemo() {
  console.log("store----", store);
  const state = store.getState();

  return (
    <div>
      <p>{state.mes}</p>
      <button
        onClick={() => {
          store.dispatch({
            type: "changeMes",
            payload: "world",
          });
          console.log("state---", state);
        }}
      >
        修改mes
      </button>
    </div>
  );
}

function StoreDemo() {
  console.log("store----", store);
  const state = store.getState();

  return (
    <div>
      <p>{state.mes}</p>
      <button
        onClick={() => {
          store.dispatch({
            type: "changeMes",
            payload: "world",
          });
          console.log("state---", state);
        }}
      >
        修改mes
      </button>
    </div>
  );
}
export default StoreDemo;
