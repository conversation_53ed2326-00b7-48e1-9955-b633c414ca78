import React, { lazy, Suspense } from "react";
import { Routes, Route, Navigate, Link } from "react-router-dom";
// import Page1 from "./page1";
// import Page2 from "./page2";
// import Page3 from "./page3";
// import Son1 from "./Son1";
// import Son2 from "./Son2";
const Page1 = lazy(() => import("./page1"));
const Page2 = lazy(() => import("./page2"));
const Page3 = lazy(() => import("./page3"));
const Son1 = lazy(() => import("./Son1"));
const Son2 = lazy(() => import("./Son2"));

const token = localStorage.getItem("token");

/**
 * 导航页
 */
function Nav() {
  return (
    <div>
      <Link to="/page1/son1">嵌套路由page1-son1</Link>
      <Link to="/page3/123/ian">动态路由page3</Link>
      <Link to="/page2?a=1&b=2">查询参数page2</Link>
    </div>
  );
}

/**
 * 路由集中管理
 */
export default function RouterPages() {
  return (
    <div>
      <Nav />
      <Suspense fallback={<div>页面加载中</div>}>
        <Routes>
          <Route path="/page1" element={<Page1 />}>
            <Route path="son1" element={<Son1 />} />
            <Route path="son2" element={<Son2 />} />
          </Route>
          {
            <Route
              path="/page2"
              element={token ? <Page2 /> : <Navigate to="/page1" replace />}
            ></Route>
          }
          <Route path="/page3/:id/:name" element={<Page3 />} />
          {/* 兜底重定向到首页 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </div>
  );
}
