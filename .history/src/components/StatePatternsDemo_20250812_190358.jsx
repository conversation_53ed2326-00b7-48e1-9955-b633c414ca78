import React, { useState } from "react";

const StatePatternsDemo = () => {
  // 1. 函数式更新（避免闭包陷阱）
  const [count, setCount] = useState(0);

  // 2. 对象状态更新
  const [user, setUser] = useState({ name: "Initial Name", age: 25 });

  // 3. 数组状态更新
  const [todos, setTodos] = useState([
    { id: 1, text: "Learn React", completed: false },
    { id: 2, text: "Build an app", completed: false },
  ]);

  // 演示闭包陷阱和函数式更新
  const handleBadIncrement = () => {
    setTimeout(() => {
      // 这是错误的方式 - 可能使用旧的 count 值
      setCount(count + 1);
    }, 1000);
  };

  const handleGoodIncrement = () => {
    setTimeout(() => {
      // 这是正确的方式 - 始终使用最新的 count 值
      setCount((prev) => prev + 1);
      console.log("定时器触发了");
    }, 1000);
  };

  // 演示对象状态更新
  const handleUpdateUser = () => {
    // 错误的方式 - 直接修改状态
    // user.name = 'New Name'; // 不要这样做！

    // 正确的方式 - 不可变更新
    setUser((prev) => ({ ...prev, name: "Updated Name" }));
  };

  // 演示数组状态更新
  const handleAddTodo = () => {
    const newTodo = {
      id: Date.now(), // 简单生成唯一 ID
      text: `New Todo ${todos.length + 1}`,
      completed: false,
    };
    // 添加元素到数组
    setTodos((prev) => [...prev, newTodo]);
  };

  const handleDeleteTodo = (id) => {
    // 从数组中删除元素
    setTodos((prev) => prev.filter((todo) => todo.id !== id));
  };

  const handleToggleTodo = (id) => {
    // 修改数组中的元素
    setTodos((prev) =>
      prev.map((todo) =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo,
      ),
    );
  };

  return (
    <div style={{ padding: "20px", border: "1px solid #ccc", margin: "10px" }}>
      <h2>React State Patterns Demo</h2>

      {/* 1. 函数式更新 */}
      <div>
        <h3>1. 函数式更新 (计数器)</h3>
        <p>Count: {count}</p>
        <button onClick={handleBadIncrement}>Bad Increment (setTimeout)</button>
        <button onClick={handleGoodIncrement}>
          Good Increment (setTimeout)
        </button>
      </div>

      {/* 2. 对象状态更新 */}
      <div>
        <h3>2. 对象状态更新 (用户信息)</h3>
        <p>
          User: {user.name}, Age: {user.age}
        </p>
        <button onClick={handleUpdateUser}>Update User Name</button>
      </div>

      {/* 3. 数组状态更新 */}
      <div>
        <h3>3. 数组状态更新 (待办事项)</h3>
        <button onClick={handleAddTodo}>Add Todo</button>
        <ul>
          {todos.map((todo) => (
            <li
              key={todo.id}
              style={{
                textDecoration: todo.completed ? "line-through" : "none",
              }}
            >
              <span>{todo.text}</span>
              <button
                onClick={() => handleToggleTodo(todo.id)}
                style={{ marginLeft: "10px" }}
              >
                {todo.completed ? "Undo" : "Complete"}
              </button>
              <button
                onClick={() => handleDeleteTodo(todo.id)}
                style={{ marginLeft: "10px" }}
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default StatePatternsDemo;

