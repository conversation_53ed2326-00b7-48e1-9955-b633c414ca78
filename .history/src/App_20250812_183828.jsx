import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import StoreDemo from "./pages/storeDemo.jsx";
import StatePatternsPage from "./pages/StatePatternsPage.jsx";
import store from "@/store";
import { Provider } from "react-redux";
import StatePatternsDemo from "./components/StatePatternsDemo.jsx";

export default function App() {
  return (
    <BrowserRouter>
      <Provider store={store}>
        <StatePatternsDemo />
        <Routes>
          <Route path="/" element={<StoreDemo />} />
          <Route path="/state-patterns" element={<StatePatternsPage />} />
        </Routes>
      </Provider>
    </BrowserRouter>
  );
}

