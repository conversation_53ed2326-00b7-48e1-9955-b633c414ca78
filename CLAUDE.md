# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a React learning project designed for developers with Vue background. It provides a structured 3-day learning path to master React concepts and provides side-by-side comparisons with Vue.

## Build and Development Commands

- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint for code quality
- `npm run preview` - Preview production build locally

## Architecture Structure

### Core Technologies
- **React 19.1.0** with modern function components
- **Vite** for build tooling and dev server
- **React Router DOM 7.6.3** for client-side routing
- **Redux 9.2.0** for state management
- **Prop-Types** for component type checking

### Directory Structure
```
src/
├── components/          # Reusable UI components
├── pages/              # Route-based page components organized by topic
├── hooks/              # Custom React hooks including localStorage utilities
├── store/              # Redux store configuration
├── services/           # API services (placeholder for future use)
├── utils/              # Utility functions
├── assets/             # Static assets like images
└── style/              # CSS modules for component-specific styles
```

### Learning Modules
- **HocDemo** - Higher Order Components demonstration
- **PerformanceOptimizationDemo** - React performance optimization patterns  
- **contextDemo** - React Context API usage examples
- **functionDemo** - Function component patterns
- **routerPages** - React Router setup examples including nested routing
- **storeDemo** - Redux store integration examples

### Key Concepts Covered
The codebase demonstrates the complete transition from Vue concepts to React:
- JSX syntax vs Vue template syntax
- Props handling patterns
- State management with useState vs Vue's reactive system
- useEffect vs Vue lifecycle hooks
- Router configuration differences
- Context API vs Vuex patterns