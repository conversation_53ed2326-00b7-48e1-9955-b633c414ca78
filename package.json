{"name": "le-qu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed"}, "dependencies": {"classnames": "^2.5.1", "normalize.css": "^8.0.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "redux": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@playwright/test": "^1.49.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}