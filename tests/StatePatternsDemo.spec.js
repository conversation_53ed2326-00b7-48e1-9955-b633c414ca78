import { test, expect } from "@playwright/test";

test.describe("StatePatternsDemo 状态更新模式测试", () => {
  test.beforeEach(async ({ page }) => {
    // 启动开发服务器并导航到组件页面
    await page.goto("http://localhost:5173");

    // 等待 StatePatternsDemo 组件加载
    await page.waitForSelector("text=React State Patterns Demo");
  });

  test("错误的增量方法 - 闭包陷阱测试", async ({ page }) => {
    console.log("🧪 测试错误的增量方法（闭包陷阱）");

    // 获取初始计数值
    const initialCount = await page.textContent("text=Count:");
    console.log(`初始计数: ${initialCount}`);

    // 快速点击错误的按钮5次（在1秒内）
    const badButton = page.locator('button:has-text("Bad Increment")');

    console.log("⚡ 快速点击错误按钮5次...");
    for (let i = 0; i < 5; i++) {
      await badButton.click();
      console.log(`点击第 ${i + 1} 次`);
    }

    // 等待1.5秒让所有定时器触发
    console.log("⏳ 等待1.5秒让定时器触发...");
    await page.waitForTimeout(1500);

    // 检查最终计数值
    const finalCount = await page.textContent("text=Count:");
    console.log(`最终计数: ${finalCount}`);

    // 验证：由于闭包陷阱，计数应该只增加1，而不是5
    const countValue = parseInt(finalCount.replace("Count: ", ""));
    expect(countValue).toBe(1); // 只增加了1，不是5

    console.log("✅ 验证通过：错误方法确实只增加了1");
  });

  test("正确的增量方法 - 函数式更新测试", async ({ page }) => {
    console.log("🧪 测试正确的增量方法（函数式更新）");

    // 获取初始计数值
    const initialCount = await page.textContent("text=Count:");
    console.log(`初始计数: ${initialCount}`);

    // 快速点击正确的按钮3次
    const goodButton = page.locator('button:has-text("Good Increment")');

    console.log("⚡ 快速点击正确按钮3次...");
    for (let i = 0; i < 3; i++) {
      await goodButton.click();
      console.log(`点击第 ${i + 1} 次`);
    }

    // 等待1.5秒让所有定时器触发
    console.log("⏳ 等待1.5秒让定时器触发...");
    await page.waitForTimeout(1500);

    // 检查最终计数值
    const finalCount = await page.textContent("text=Count:");
    console.log(`最终计数: ${finalCount}`);

    // 验证：使用函数式更新，计数应该正确增加3
    const countValue = parseInt(finalCount.replace("Count: ", ""));
    expect(countValue).toBe(3); // 正确增加了3

    console.log("✅ 验证通过：正确方法增加了3");
  });

  test("对比测试 - 同时测试两种方法", async ({ page }) => {
    console.log("🧪 对比测试两种方法");

    // 重置计数器（如果需要的话，刷新页面）
    await page.reload();
    await page.waitForSelector("text=React State Patterns Demo");

    const badButton = page.locator('button:has-text("Bad Increment")');
    const goodButton = page.locator('button:has-text("Good Increment")');

    // 先用错误方法点击2次
    console.log("⚡ 错误方法点击2次...");
    await badButton.click();
    await badButton.click();

    // 等待一小段时间
    await page.waitForTimeout(100);

    // 再用正确方法点击2次
    console.log("⚡ 正确方法点击2次...");
    await goodButton.click();
    await goodButton.click();

    // 等待所有定时器触发
    console.log("⏳ 等待2秒让所有定时器触发...");
    await page.waitForTimeout(2000);

    // 检查最终结果
    const finalCount = await page.textContent("text=Count:");
    console.log(`最终计数: ${finalCount}`);

    // 期望结果：错误方法贡献1，正确方法贡献2，总共3
    const countValue = parseInt(finalCount.replace("Count: ", ""));
    expect(countValue).toBe(3);

    console.log("✅ 对比测试通过");
  });

  test("控制台日志验证", async ({ page }) => {
    console.log("🧪 测试控制台日志输出");

    // 监听控制台消息
    const consoleMessages = [];
    page.on("console", (msg) => {
      consoleMessages.push(msg.text());
      console.log(`浏览器控制台: ${msg.text()}`);
    });

    const badButton = page.locator('button:has-text("Bad Increment")');
    const goodButton = page.locator('button:has-text("Good Increment")');

    // 点击错误按钮
    await badButton.click();
    // 点击正确按钮
    await goodButton.click();

    // 等待定时器触发
    await page.waitForTimeout(1500);

    // 验证控制台消息
    expect(consoleMessages).toContain("错误的方式");
    expect(consoleMessages).toContain("定时器触发了");

    console.log("✅ 控制台日志验证通过");
  });

  test("状态更新时机分析", async ({ page }) => {
    console.log("🧪 分析状态更新时机");

    // 监听页面变化
    let countChanges = [];

    // 定期检查计数值变化
    const checkCount = async () => {
      const countText = await page.textContent("text=Count:");
      const countValue = parseInt(countText.replace("Count: ", ""));
      countChanges.push({
        time: Date.now(),
        value: countValue,
      });
    };

    await checkCount(); // 初始值

    const goodButton = page.locator('button:has-text("Good Increment")');

    // 快速点击3次
    for (let i = 0; i < 3; i++) {
      await goodButton.click();
      await page.waitForTimeout(50); // 短暂等待
      await checkCount();
    }

    // 等待定时器完成
    await page.waitForTimeout(1200);
    await checkCount();

    console.log("计数变化历史:", countChanges);

    // 验证最终值
    expect(countChanges[countChanges.length - 1].value).toBe(3);

    console.log("✅ 状态更新时机分析完成");
  });
});
