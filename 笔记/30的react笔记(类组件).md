# React 的思想

1. 更少的封装，更高的自由度，能够让使用者更加高度的去自定义实现，不用像 Vue 一样必须遵守规则，在 React 里只需要遵守 js 规则。
2. 纯粹的 js，不用去背什么特殊指令。

# 1. jsx 中渲染不同内容的规则

- 字符串、数组：直接渲染
- 对象：只能渲染 element 对象
- 数组：把数组中的每一项单独渲染
- 表达式：运行表达式
- 方法：无法渲染
- 布尔值：不渲染任何内容
- undefined\null：不渲染任何内容

# 2. jsx 绑定事件

- on+事件名称(首字母大写)
- 一定要赋值给事件一个方法名（如方法是`fn`，只能写`fn`，不能写`fn()`）

### React 的函数绑定注意事项（Class 语法）

1. 不做处理的情况下，`this`会指向`undefined`
2. 给事件绑定的一定要是个方法，不要直接调用方法，调用方法只会在页面初始化时调用一次。

#### 解决 this 为 undefined 的方法

1.  使用 bind 硬绑定 this (推荐，传参也是用 bind)

```jsx
class App extends React.Component {
  handleClick() {
    console.log(this);
  }
  render() {
    return (
      <div>
        <button onClick={this.handleClick.bind(this)}>Click me</button>
      </div>
    );
  }
}
```

2. 方法本身改为箭头函数

```jsx
class App extends React.Component {
  handleClick = () => {
    console.log(this);
  };
  render() {
    return (
      <div>
        <button onClick={this.handleClick}>Click me</button>
      </div>
    );
  }
}
```

3. 绑定匿名箭头函数

```jsx
class App extends React.Component {
  render() {
    return (
      <div>
        <button
          onClick={() => {
            console.log(this);
          }}
        >
          Click me
        </button>
      </div>
    );
  }
}
```

### 事件绑定的其他操作

1. 传递参数

- React 中绑定给事件的是一个方法名，所以传参只能用 bind(`bind` 会返回一个新函数)。

```jsx
class App extends React.Component {
  handleClick = (arg1, arg2) => {
    console.log(arg1, arg2);
  };
  render() {
    return (
      <div>
        <button onClick={this.handleClick.bind(this, 1, 2)}>Click me</button>
      </div>
    );
  }
}
```

2. 获取事件对象

注意这里获取的事件对象是“合成事件对象”，原生的事件对象保存在合成对象的 `nativeEvent` 属性

```jsx
class App extends React.Component {
  // 1. 有参数时，事件对象在参数后面一个参数
  handleClick = (arg1, arg2, e) => {
    console.log(e);
  };
  // 2. 无参数，事件对象就是第一个参数
  handleClick = (e) => {
    console.log(e);
  };

  render() {
    return (
      <div>
        <button onClick={this.handleClick.bind(this, 1, 2)}>
          有参数获取事件对象
        </button>
        <button onClick={this.handleClick}>无参数获取事件对象</button>
      </div>
    );
  }
}
```

3. 阻止默认行为,冒泡等

```jsx
class App extends React.Component {
  handleClick = (e) => {
    // 阻止事件冒泡 - 防止事件向上传播到父元素
    e.stopPropagation();
    // 阻止默认行为 - 比如阻止表单提交、链接跳转等
    e.preventDefault();
  };

  render() {
    return (
      <div>
        <button onClick={this.handleClick}>点击</button>
      </div>
    );
  }
}
```

# React 中的响应式数据

### React 响应式体系的原理

1. React 不能像 Vue 一样直接修改，触发更新。
2. React 能修改值，但无法触发更新。因为 React 没有像 Vue 一样监听 get 和 set，而是在调用`setState`的时候调用 React 的更新操作。

### React 中的数据类型

| 数据类型            | 是否动态 | 变化方式                                 | 变化是否触发重新渲染 |
| ------------------- | -------- | ---------------------------------------- | -------------------- |
| `state`             | ✅ 是    | 通过 `setState` 或 `useState` 的更新函数 | ✅ 是                |
| `props`             | ✅ 是    | 父组件重新渲染传入新的值                 | ✅ 是                |
| `context`           | ✅ 是    | 通过 Context Provider 的 value 变化      | ✅ 是                |
| `ref` 对象          | ❌ 否    | ref 对象本身不变                         | ❌ 否                |
| `ref.current`       | ✅ 是    | 组件挂载/卸载或手动修改                  | ❌ 否                |
| 普通类属性/实例变量 | ✅ 是    | 直接赋值                                 | ❌ 否                |
| 本地变量（函数内）  | ✅ 是    | 直接赋值                                 | ❌ 否                |
| 全局变量            | ✅ 是    | 直接赋值                                 | ❌ 否                |

> #### 重要区别：触发重新渲染的数据 vs. 不触发重新渲染的数据

> 会触发重新渲染的数据变化：

- **`state` 变化**：通过 `setState` 或 `useState` 的更新函数
- **`props` 变化**：父组件重新渲染传入新的 props
- **`context` 变化**：Context Provider 的值变化

> 不会触发重新渲染的数据变化：

- **`ref.current` 变化**：可以变化但不会触发重新渲染
- **实例属性变化**：不在`state`中的类属性
- **局部变量变化**：函数组件内的普通变量

### React 中修改 `state` 需要使用 `setState` 才能触发更新

```jsx
class App extends React.Component {
  state = {
    count: 0,
  };
  handleClick = () => {
    // setState 可以传入一个函数，但是这个函数也要返回对象。
    // this.setState(() => {
    //   return { count: this.state.count + 1 };
    // });

    this.setState(
      {
        count: this.state.count + 1,
      },
      () => {
        // setState是异步的，在setState的回调函数中才能获取最新的state
        console.log(this.state.count);
      }
    );
  };
  render() {
    return (
      <div>
        <button onClick={this.handleClick}>Click me</button>
        <p>{this.state.count}</p>
      </div>
    );
  }
}
```

### `setState` 工作流程图解

`调用 setState ` -> `给入一个对象` -> `给入的对象和state对象进行浅合并` -> `合并后调用更新方法进行更新`

关键点：

1. 通过浅合并来修改数据
2. 调用`setState` 方法会触发更新，直接修改 state 并不会触发更新

### `setState` 方法的一些特性

1. `setState` 方法多次修改，会合并为一次，统一更新
2. `setState` 返回会触发更新，不管你是否有修改，这造成了一个问题，重复修改为相同的值也会让组件更新.
3. **一定不要在`render`里直接`setState`，会造成死循环。**

### `setState` 修改对象注意事项

由于`setState`是浅合并，所以修改对象时，会直接用新对象替换旧对象。所以我们要用展开运算符来保留原对象的所有属性。

```jsx
class App extends React.Component {
  state = {
    obj: {
      a: 1,
      b: 2,
    },
  };

  handleClick = () => {
    this.setState({
      obj: {
        // 展开原对象，保留所有对象属性。
        ...this.state.obj,
        a: this.state.obj.a + 1,
      },
    });
  };
  render() {
    return (
      <div>
        <button onClick={this.handleClick}>Click me</button>
        <p>a: {this.state.obj.a}</p>
        <p>b: {this.state.obj.b}</p>
      </div>
    );
  }
}
```

# PureComponent 和 Component

### PureComponent 和 Component 的区别

区别：PureComponent 性能上优于 Component，但是 PureComponent 有它的局限性。
Component 会导致不必要的重新渲染，尤其是在频繁更新但是**实际内容没有变化**的情况下。
PureComponent 通过跳过不必要的渲染来提高性能，适合接收简单 props 的展示型组件。

**React.PureComponent** 的浅比较只检查对象引用是否变化，不检查嵌套对象内部的值。

### PureComponent 下对于对象和数组的修改

因为 PureComponent 会根据 state 是否改变来决定是否更新，而我们对于对象、数组这样的引用类型判断它是否修改的原理是看它的内存地址，而不是内容。
所以 PureComponent 下修改对象和数组，一定要赋予一个新对象，所以一般不直接操作原对象，而是先拷贝一份，再进行操作。

# React 没有指令

React 没有像 Vue 一样的指令，一切操作本质上都是我们通过运算生成不同的内容，拿去渲染，得到不同的页面。

### 条件渲染的本质 (类似 v-if)

原则：

1. React 渲染`undefined`、`null`、空字符串、`false` 不会渲染成任何内容
2. 如果渲染一个 jsx 编写的 html 元素，就会渲染成页面上的内容。

```
                   编写一个js逻辑运算
                            |
          +-----------------+------------------+
          |                                    |
        为false时                          为true时
          |                                    |
          v                                    v
  返回一个false或空字符串                 返回要显示的html结构，
  则不渲染任何内容，                      显示为页面上的内容
  这样就不会显示内容
```

```jsx
class App extends React.Component {
  state = {
    isShow: true,
  };

  render() {
    return (
      <div>
        <div>
          <button onClick={() => this.setState({ isShow: !this.state.isShow })}>
            {this.state.isShow ? "隐藏" : "显示"}
          </button>
        </div>
        {/*条件渲染*/}
        <div>{this.state.isShow && <div>Hello</div>}</div>
      </div>
    );
  }
}
```

### 列表循环的本质 (类似 v-for)

原则：

1. 渲染一个数组会把数组中的每一项单独取出渲染
2. 那么我们编写一个里面存放的都是 html 结构的数组，就会渲染成列表

```
原数据 --循环--> 把原数组生成为一个存放 ----> jsx渲染存储着html结构的新
               列表每一行元素的新数组       数组，渲染为列表
```

### Vue 指令开发与 React 的 jsx 开发

Vue：各种效果用指令编写，对于简单的控制非常容易
React：各种效果都通过逻辑运算产出对应的内容渲染，能够完整控制整个过程

### 表单绑定

React 中很多思路都是按原生的操作去做的，表单绑定也是如此。

原生表单获取表单输入值，可以通过监听 `input`、`change` 等事件，然后获取`e.target.value`
如果要设置表单的值，通常设置`value`属性，如果是选择框则是`checked`属性。

### 表单中的受控组件和非受控组件

#### 受控组件

表单的 value 绑定到了 state 的上，并且做了事件监听来通过`setState`设置值。 value 可以由使用者手动去设置，也就是进行了双向绑定的，可以修改 state 数据影响到表单显示的值。

#### 非受控组件

表单的 value 没有绑定到 state 上，只做了事件监听来获取值。表单的值由 DOM 自身管理，只能获取它的值，不能直接设置它的值。

#### input 绑定(受控组件)

```jsx
class App extends React.PureComponent {
  state = {
    value: "",
  };
  handleChange = (e) => {
    this.setState({
      value: e.target.value,
    });
  };
  render() {
    <div style={{ padding: 24 }}>
      <h2>React input 受控组件示例</h2>
      <input
        type="text"
        value={this.state.value}
        onChange={this.handleChange}
        placeholder="请输入内容"
      />
      <p>当前输入内容：{this.state.value}</p>
    </div>;
  }
}
```

#### checkbox 绑定(受控组件)

```jsx
class App extends React.PureComponent {
  state = {
    checkedArr: ["1", "2"],
  };

  handleCheck = (e) => {
    let _arr = [...this.state.checkedArr];
    if (e.target.checked) {
      _arr.push(e.target.value);
    } else {
      _arr = _arr.filter((item) => item !== e.target.value);
    }
    this.setState({
      checkedArr: _arr,
    });
  };
  handleClear = () => {
    this.setState({
      checkedArr: [],
    });
  };
  render() {
    return (
      <div style={{ padding: 24 }}>
        <div>
          <input
            checked={this.state.checkedArr.includes("1")}
            type="checkbox"
            value="1"
            onChange={this.handleCheck}
          />
          选项1
          <input
            checked={this.state.checkedArr.includes("2")}
            type="checkbox"
            value="2"
            onChange={this.handleCheck}
          />
          选项2
          <input
            checked={this.state.checkedArr.includes("3")}
            type="checkbox"
            value="3"
            onChange={this.handleCheck}
          />
          选项3
        </div>
        <p>当前选中的值：{this.state.checkedArr.join(",")}</p>
        <button onClick={this.handleClear}>清空</button>
      </div>
    );
  }
}
```

# props 是 React 的核心

在 React 中，一切写在组件上的属性和子节点都被规划为 props。
所以 props 是 react 很多功能的根本。父子传值，插槽全部都是基于 props，不像 Vue 有事件监听，emit，专门的插槽这一类东西。

```jsx
// 父组件;
class App extends React.PureComponent {
  state = {
    msg: "我是父组件",
  };
  render() {
    return (
      <div>
        <Son msg={this.state.msg}>
          <p>我是Son组件的子节点</p>
        </Son>
      </div>
    );
  }
}
```

```jsx
// 子组件

class Son extends React.PureComponent {
  state = {
    sonMsg: "我是子组件",
  };
  render() {
    console.log(this.props);
    // {msg:"我是父组件",children:{...}}
    // 如果只有一个插槽children就是对象，如果是多个就是数组
    return (
      <div>
        <p>我是子组件</p>
        <p>父组件传给我的值：{this.props.msg}</p>
      </div>
    );
  }
}
```

### props 的类型验证和默认值

```
类型验证 ──▶ 设置静态属性 propTypes ──▶ 编写每个属性的验证方法

默认值   ──▶ 设置静态属性 defaultProps ──▶ 编写每个属性的默认值
```

类型验证：在 React19 中，PropTypes 验证被完全移除了，现在只能用 TS 来进行验证。

```jsx
// 旧版本
import PropTypes from "prop-types";

// 函数组件
function Greeting({ name, age }) {
  return (
    <div>
      <h1>你好, {name}!</h1>
    </div>
  );
}

// PropTypes 类型校验
Greeting.propTypes = {
  // 使用PropTypes或者手写代码验证
  name: PropTypes.string.isRequired, // 必需的字符串
  // 手写验证
  name(props, propName, componentName) {
    if (typeof props[propName] !== "string") {
      return new Error(
        `组件${componentName}的${propName}属性必须是字符串类型，但现在是${typeof props[
          propName
        ]}`
      );
    }
  },
};
```

```jsx
// React 19

// 定义 Props 类型
interface GreetingProps {
  name: string; // 必需的字符串
  age?: number; // 可选的数字
}

// 函数组件（使用 ES6 默认参数）
function Greeting({ name, age = 25 }: GreetingProps) {
  return (
    <div>
      <h1>你好, {name}!</h1>
    </div>
  );
}
```

默认值：

```jsx
// 旧方式
function Button({ label }) {
  return <button>{label}</button>;
}
Button.defaultProps = {
  label: "Click me",
};

// 新方式（推荐）
function Button({ label = "Click me" }) {
  return <button>{label}</button>;
}
```

### 模拟 Vue 中的插槽

插槽本质上就是子组件的 html 内容需要父组件传入，在 jsx 的加持下，我们可以把 html 像普通的字符串、数字一样传递，所以查抄只需要直接作为 props 传入即可

```
把要插入的内容作为 ----> 子组件渲染传递过来的
props传递给子组件        props
```

普通插槽:

```jsx
// 父组件
class App extends React.PureComponent {
  render() {
    return (
      <div>
        <Son>
          <p>我是Son组件的子节点</p>
        </Son>
      </div>
    );
  }
}
```

```jsx
// 子组件
class Son extends React.PureComponent {
  render() {
    return (
      <div>
        <p>我是子组件</p>
        {/*这里渲染props传递过来的插槽内容*/}
        {this.props.children}
      </div>
    );
  }
}
```

具名插槽

```jsx
// 父组件
class App extends React.PureComponent {
  render() {
    return (
      <div>
        <Son slotA={<p>我是slotA</p>}></Son>
      </div>
    );
  }
}
```

```jsx
// 子组件
class Son extends React.PureComponent {
  render() {
    {
      /*渲染具名插槽，slotA*/
    }
    return <div>{this.props.slotA}</div>;
  }
}
```

作用域插槽

- 父组件插入的内容，可以接收子组件的数据，可以进行更灵活的内容渲染。

```jsx
// 父组件
class App extends React.PureComponent {
  render() {
    return (
      <div>
        {/*传递一个函数，接收的参数是son传递的值，返回值是要渲染的html*/}
        <Son
          scopeSlot={(scopeData) => <p>我是scopeSlot{scopeData.msg}</p>}
        ></Son>
      </div>
    );
  }
}
```

```jsx
// 子组件
class Son extends React.PureComponent {
  render() {
    return (
      <div>
        <p>我是子组件</p>
        {/* 调用父组件传递过来的函数，并传递子组件的数据 */}
        {this.props.scopeSlot({ msg: "我是scopeData" })}
      </div>
    );
  }
}
```

### 子传父

React 中实现子组件修改父组件 state，需要父组件把一个函数作为 props 传递给子组件，子组件通过调用这个函数，把数据传回父组件，再由父组件进行修改 state。

```
父组件把方法传递给子组件 ----> 子组件调用 ----> 调用时传递参数 ----> 父组件执行方法
                          传递过来的方法                       进行操作，比如修改
                                                            state（注意this）

```

```jsx
// 父组件
class App extends React.PureComponent {
  state = {
    msg: "我是父组件",
  };
  changeParentMsg = (msg) => {
    // 注意：要用箭头函数，或者this.changeParentMsg.bind(this)。
    // 不然this会只想props，因为son是通过this.props.changeParentMsg调用的
    this.setState({
      msg,
    });
  };
  render() {
    return (
      <div>
        <p>父组件的值：{this.state.msg}</p>
        <Son changeParentMsg={this.changeParentMsg}></Son>
      </div>
    );
  }
}
```

```jsx
// 子组件
class Son extends React.PureComponent {
  render() {
    return (
      <div>
        <button
          onClick={() => this.props.changeParentMsg("我是子组件传递的数据")}
        >
          点击我，改变父组件的值
        </button>
      </div>
    );
  }
}
```

#### 兄弟组件传值

1. (推荐)**子 1 ----> 父组件 ----> 子 2**
2. eventbus

# React 的 class 和 style

class 类名设置

1. 必须写为 className
2. 类名和样式写在 css 文件中
3. 必须接收一个字符串

style 内联

1. 必须接收一个对象

### React 中 css 注意事项

css 文件命名需要使用`Xxx.module.css` 才能避免类名影响全局

可以使用 classnames 库来提高开发速度。

```jsx
import styles from "@/style/Son.module.css";

// classNames 可以绑定一个对象，方便根据条件动态切换样式类名
import classNames from "classnames/bind";

// classNames.bind 配合xxx.module.css，可以让你可以用“原始类名”写法，自动映射成 CSS Modules 的真实类名(样式隔离的类名)。
const cx = classNames.bind(styles);

class Son extends React.PureComponent {
  state = {
    sonMsg: "我是子组件",
    hasSon1: false,
  };
  render() {
    return (
      <div
        className={cx("son", {
          son1: this.state.hasSon1,
        })}
      >
        <div>我是子组件</div>
        <button onClick={() => this.setState({ hasSon1: !this.state.hasSon1 })}>
          点击我，切换子组件的样式
        </button>
      </div>
    );
  }
}
```

# React 生命周期

## 类组件生命周期

#### 1. 挂载（Mount）阶段

```mermaid
graph TD
  A1["constructor (初始化)"] --> A2["static getDerivedStateFromProps(注意，这个名字会产生误导，这个生命周期是渲染流程的固定一环，无论渲染由什么触发的。)"]
  A2 --> A3["render (渲染UI)"]
  A3 --> A4["componentDidMount (挂载后)"]
```

#### 2. 更新（Update）阶段

```mermaid
graph TD
  B1["props/state变化"] --> B2["static getDerivedStateFromProps(注意，这个名字会产生误导，这个生命周期是渲染流程的固定一环，无论渲染由什么触发的。)"]
  B2 --> B3["shouldComponentUpdate (要不要更新)"]
  B3 -- "false" --> B8["停止更新"]
  B3 -- "true" --> B4["render (渲染UI)"]
  B4 --> B5["getSnapshotBeforeUpdate (更新前快照)"]
  B5 --> B6["React更新DOM"]
  B6 --> B7["componentDidUpdate (更新后)"]
```

### 重点生命周期

- `render`
  - 通过 `render` 函数的执行来决定组件渲染什么内容，所以无论更新还是初次挂在都必须执行 `render`。
  - 应用：一般不做什么事情，尤其不要在其中更改`state`，会导致死循环。
- `componentDidMount`
  - 组件挂载完成。
  - 应用：一般用来做一些页面初始化操作，比如初始请求，echart 绘制，获取真实 DOM 等， 相当于就是 vue 的`mounted`里能做的事。
- `shouldComponentUpdate`
  - 更新阶段调用，如果`return false` 则不会执行`render`函数继续更新，从而达到阻止更新的效果。
  - 应用：一般用来做性能优化。（`pureComponent`也是用`shouldComponentUpdate`做的）
- `componentDidUpdate`
  - 更新完成，等同于 Vue 的`updated`
- `componentWillUnmount`
  - 组件即将卸载。
  - 应用：通常做全局事件监听的卸载，定时器，计时器的卸载，用来优化性能。

### React 获取元素和子组件

ref 用于获取真实 dom，和 Vue 中的 `ref` 功能一样。

注意事项：

1. ref 必须在挂载后才能获取,通常在`componentDidMount`
2. ref 获取组件，不能获取函数组件。

```jsx
import React from "react";
class Son2 extends React.Component {
  handleClick = () => {
    console.log("我是子组件2的点击事件");
  };
  render() {
    return <button onClick={this.handleClick}>点击</button>;
  }
}

export default class App extends React.Component {
  son = React.createRef();
  div1 = React.createRef();
  componentDidMount() {
    // 挂载之后可以获取ref
    console.log(this.son.current);
    console.log(this.div1.current);
  }
  handleClick = () => {
    // 由于ref只能在挂载之后才能获取,所以在render中直接把ref获取的子组件的方法绑定到按钮上，会导致渲染报错

    // 正确写法：在父组件中创建一个中间函数handleClick,来解决这个问题。
    this.son.current.handleClick();
  };

  render() {
    return (
      <div>
        <div ref={this.div1}>123</div>
        <Son2 ref={this.son} />
        {/*错误写法，渲染会报错。因为ref只能在挂载之后获取*/}
        {/* <button onClick={this.son.current.handleClick}>
          点击我，切换子组件的样式
        </button> */}

        {/*正确写法：创建一个中间函数*/}
        <button onClick={this.handleClick}>点击我，切换子组件的样式</button>
      </div>
    );
  }
}
```

### context

类似 Vue 中的`provider`和`injected`，用于嵌套很深的爷孙组件之间传值。
注意事项：

1. 子组件使用父组件创建的 context 对象，不能自己创建。

#### 单个 context 推荐获取方式

1. 创建一个文件(ParentContext.js)用来存放 Context。
   - 如果 context 定义在父组件中，孙子组件从父组件导入，会导致依赖循环。index -> Son -> Grandson -> index，形成了一个闭环。
2. 在父组件中使用 ParentContext.js
3. 在孙子组件使用 ParentContext.js

```jsx
// ParentContext.js
import React from "react";
// 首字母大写
export const ParentContext = React.createContext();
```

```jsx
// 父组件
import React from "react";
import { ParentContext } from "./ParentContext";
import Son from "./Son";

export default class ContextDemo extends React.Component {
  state = {
    msg: "我是父组件",
  };
  render() {
    return (
      <div>
        {this.state.msg}
        <ParentContext.Provider value={{ name: "张三", age: 18 }}>
          <Son />
        </ParentContext.Provider>
      </div>
    );
  }
}
```

```jsx
import Grandson from "./Grandson";
// 子组件
class Son extends React.Component {
  render() {
    return (
      <div>
        我是子组件
        <Grandson />
      </div>
    );
  }
}
```

```jsx
// 孙子组件
import React from "react";
// 导入祖先组件创建的context
import ParentContext from "./ParentContext";
class Grandson extends React.Component {
  // `static contextType =` 是固定写法，写上这行context才会绑定到this上。
  static contextType = ParentContext;

  render() {
    console.log(this.context);
    return (
      <div>
        我是孙子组件
        <div>
          <div>通过contextType获取</div>
          <div>{this.context.name}</div>
          <div>{this.context.age}</div>
        </div>
      </div>
    );
  }
}
export default Grandson;
```

#### 多个 context 推荐获取方式

这段代码展示了一个 React 类组件 (`Grandson`) 如何从两个不同的上下文 (`ParentContext` 和 `ParentContext2`) 中同时获取数据。

```jsx
// 父组件
import React from "react";
import { ParentContext, ParentContext2 } from "./ParentContext";

import Son from "./Son";

export default class ContextDemo extends React.Component {
  state = {
    msg: "我是父组件",
  };
  render() {
    return (
      <div>
        {this.state.msg}
        <ParentContext.Provider
          value={{
            name: "小红",
            age: 19,
          }}
        >
          <ParentContext2.Provider
            value={{
              hobby: "篮球",
            }}
          >
            <Son />
          </ParentContext2.Provider>
        </ParentContext.Provider>
      </div>
    );
  }
}
```

```jsx
// 子组件
import React from "react";
import Grandson from "./Grandson";

// 子组件
class Son extends React.Component {
  state = {
    sonMsg: "我是子组件",
  };
  render() {
    return (
      <div>
        {this.state.sonMsg}
        <Grandson />
      </div>
    );
  }
}
export default Son;
```

```jsx
// 孙子组件

import React from "react";
// 导入祖先组件创建的context
import { ParentContext, ParentContext2 } from "./ParentContext";
class Grandson extends React.Component {
  render() {
    console.log(this.context);
    return (
      <div>
        我是孙子组件
        <div>
          <ParentContext.Consumer>
            {(value) => {
              return (
                <ParentContext2.Consumer>
                  {(value2) => {
                    return (
                      <div>
                        <div>{value.name}</div>
                        <div>{value.age}</div>
                        <div>{value2.hobby}</div>
                      </div>
                    );
                  }}
                </ParentContext2.Consumer>
              );
            }}
          </ParentContext.Consumer>
        </div>
      </div>
    );
  }
}
export default Grandson;
```

可以看到从 2 个不同上下文获取数据代码嵌套已经非常深了，当有 3、4 时代码代码可读性会非常差。

#### `createContext(defaultValue)` 的 defaultValue 是什么？什么时候生效？

`defaultValue` 是静态的：

- `defaultValue` 在调用 `React.createContext(defaultValue)` 时被**一次性设定**，并且**永远不会改变**
- 不像 `<Provider>` 的 `value` 属性那样可以响应状态（`state`）或属性（`props`）的变化。访问 `defaultValue` **不会**导致组件重新渲染。

defaultValue 只在一个特定情况下生效：**当一个组件需要从 Context 获取数据，但在它的所有父级组件中都找不到一个为它提供数据的 `<Provider>` 时**。

```jsx
// App.jsx
import React from "react";
import MyComponent from "./MyComponent";

function App() {
  return (
    <div>
      <h1>Provider 不存在的情况</h1>
      {/* MyComponent 在它的祖先中找不到 MyContext.Provider */}
      <MyComponent />
    </div>
  );
}
```

在这个场景下，React 在 `MyComponent` 的上方找不到 `MyContext.Provider`，于是 `useContext(MyContext)` 只能回退去使用创建 Context 时指定的 `defaultValue`。
