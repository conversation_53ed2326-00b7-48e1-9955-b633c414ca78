# 笔记

## 问题

### 在Vite.config.ts配置`@`指向`src`，但是TS报错。

编辑器报错：`找不到模块“@/locales”或其相应的类型声明。ts(2307)`，项目运行是正常的，可以访问到@引入的文件，vite中也配置了。

原因：路径别名未被 TypeScript 识别，需要在`tsconfig.json`或者`tsconfig.app.json`中配置`paths`，不然编辑器报错。

```json
//  tsconfig.json 或 tsconfig.app.json
{
  "compilerOptions": {
    // ... 其他配置 ...
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

```ts
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import { fileURLToPath, URL } from 'url';

export default defineConfig({
  plugins: [react(), tailwindcss()],
  // 这里配置`@`指向src
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
});
```

## 记录

### 1. `i18next-browser-languagedetector` 自动保存语言，刷新自动应用

`i18next-browser-languagedetector` 自动缓存语言,刷新页面自动应用语言

```ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
  .use(LanguageDetector) // 自动检测语言
  .use(initReactI18next) // 传递 i18n 到 react-i18next
  .init({
    // ...
    // languagedetector语言检测配置
    detection: {
      // 检测顺序
      order: ['localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      // 缓存用户选择的语言
      caches: ['localStorage', 'sessionStorage'],
      // localStorage 键名
      lookupLocalStorage: 'language',
      // sessionStorage 键名
      lookupSessionStorage: 'language',
    },
  });
```
