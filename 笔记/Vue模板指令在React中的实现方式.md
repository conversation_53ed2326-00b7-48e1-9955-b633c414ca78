# Vue 模板指令在 React 中的实现方式

### 1. v-if / v-else / v-else-if（条件渲染）

Vue

```html
<div v-if="type === 'A'">A</div>
<div v-else-if="type === 'B'">B</div>
<div v-else>C</div>
```

React

```jsx
// 1. 三元运算符
{
  type === "A" ? <div>A</div> : type === "B" ? <div>B</div> : <div>C</div>;
}

// 2. 逻辑与运算符
{
  type === "A" && <div>A</div>;
}
{
  type === "B" && <div>B</div>;
}
{
  type !== "A" && type !== "B" && <div>C</div>;
}

// 3. 函数封装
function renderContent() {
  if (type === "A") return <div>A</div>;
  if (type === "B") return <div>B</div>;
  return <div>C</div>;
}

// 在JSX中调用
{
  renderContent();
}
```

### 2. v-for（列表渲染）

Vue

```html
<ul>
  <li v-for="(item, index) in items" :key="item.id">
    {{ index }} - {{ item.text }}
  </li>
</ul>
```

React

```jsx
<ul>
  {items.map((item, index) => (
    <li key={item.id}>
      {index} - {item.text}
    </li>
  ))}
</ul>
```

### 3. v-model（双向绑定）

Vue

```html
<input v-model="message" />
```

React

```jsx
const [message, setMessage] = useState("");

<input value={message} onChange={(e) => setMessage(e.target.value)} />;
```

### 4. v-bind / :（属性绑定）

```html
<div :class="{ active: isActive }" :style="{ color: textColor }">
  <img :src="imgUrl" :alt="imgAlt" />
</div>
```

React

```jsx
<div
  className={isActive ? "active" : ""}
  style={{ color: textColor }}
>
  <img src={imgUrl} alt={imgAlt} />
</div>

// 对于多个类名的情况
<div className={`base-class ${isActive ? "active" : ""} ${isLarge ? "large" : ""}`}>
```

### 5. v-on / @（事件处理）

Vue

```html
<button @click="handleClick">点击</button>
<input @input="handleInput" @focus="handleFocus" />
```

React

```jsx
<button onClick={handleClick}>点击</button>
<input onInput={handleInput} onFocus={handleFocus} />

// 传递参数
<button onClick={() => handleClick(id)}>点击</button>
```

### 6.v-show(条件显示)

Vue

```html
<div v-show="isVisible">内容</div>
```

React

```jsx
<div style={{ display: isVisible ? "" : "none" }}>内容</div>
```
